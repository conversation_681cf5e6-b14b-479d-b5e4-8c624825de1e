package com.org.panaroma.web.repo;

import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.WebApiV1Constants.FUZZY_SERACH_REQUIRED;
import static com.org.panaroma.commons.constants.WebConstants.COMMA;
import static com.org.panaroma.commons.constants.WebConstants.ENTITY_ID;
import static com.org.panaroma.commons.constants.WebConstants.HYPHEN;
import static com.org.panaroma.commons.constants.WebConstants.MULTI_MATCH_AUTOCOMPLETE;
import static com.org.panaroma.commons.constants.WebConstants.MULTI_MATCH_AUTOCOMPLETE_EXACT_MATCH_BOOST;
import static com.org.panaroma.commons.constants.WebConstants.MULTI_MATCH_OVER_KEYWORD;
import static com.org.panaroma.commons.constants.WebConstants.MULTI_MATCH_SEARCH;
import static com.org.panaroma.commons.constants.WebConstants.MULTI_MONTH_SEARCH;
import static com.org.panaroma.commons.constants.WebConstants.MULTI_VALUED_SEARCH;
import static com.org.panaroma.commons.constants.WebConstants.MULTI_VALUED_SEARCH_NESTED;
import static com.org.panaroma.commons.constants.WebConstants.NESTED;
import static com.org.panaroma.commons.constants.WebConstants.PANAROMA_SERVICE;
import static com.org.panaroma.commons.constants.WebConstants.RANGE;
import static com.org.panaroma.commons.constants.WebConstants.REGEXP_NESTED;
import static com.org.panaroma.commons.constants.WebConstants.STREAM_SOURCE;
import static com.org.panaroma.commons.constants.WebConstants.SearchContextToESMappings;
import static com.org.panaroma.commons.constants.WebConstants.TERM;
import static com.org.panaroma.commons.constants.WebConstants.TERMS;
import static com.org.panaroma.commons.constants.WebConstants.TRANSACTION_DATE_EPOCH_TIME;
import static com.org.panaroma.commons.constants.WebConstants.TRANSACTION_ID;
import static com.org.panaroma.web.exceptionhandler.ErrorCodeConstants.INTERNAL_SERVER_ERROR;
import static com.org.panaroma.web.exceptionhandler.ErrorCodeConstants.QUERY_EXCEPTION;

import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.commons.utils.Pair;
import com.org.panaroma.web.SearchContext;
import com.org.panaroma.web.dto.SearchCriteriaEnum;
import com.org.panaroma.web.dto.SearchCustomContext;
import com.org.panaroma.web.dto.SearchTxnCategory;
import com.org.panaroma.web.exceptionhandler.ExceptionFactory;
import com.org.panaroma.web.filter.FiltersFactory;
import com.org.panaroma.web.filter.IFilterType;
import com.org.panaroma.web.utility.SearchIntelligentUtility;
import com.org.panaroma.web.utility.configurablePropertyUtility.ConfigurablePropertiesHolder;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.common.unit.Fuzziness;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.MultiMatchQueryBuilder;
import org.elasticsearch.index.query.Operator;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.fetch.subphase.highlight.HighlightBuilder;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Log4j2
@Component
public class EsUtil {

	private static int socketTimeout;

	private static FiltersFactory filtersFactory;

	private static ConfigurablePropertiesHolder configurablePropertiesHolder;

	@Autowired
	public EsUtil(final FiltersFactory filtersFactory, @Value("${es-socket-timeout}") final int socketTimeout,
			final ConfigurablePropertiesHolder configurablePropertiesHolder) {
		this.filtersFactory = filtersFactory;
		this.socketTimeout = socketTimeout;
		EsUtil.configurablePropertiesHolder = configurablePropertiesHolder;
		log.info("EsUtil Fields have been initialized SocketTimeout: {}", this.socketTimeout);
	}

	public static void setSortParamInSearchRequest(final SearchSourceBuilder searchSourceBuilder) {

		searchSourceBuilder.sort(new FieldSortBuilder(TRANSACTION_DATE_EPOCH_TIME).order(SortOrder.DESC));
		searchSourceBuilder.sort(new FieldSortBuilder(TRANSACTION_ID).order(SortOrder.DESC));
		searchSourceBuilder.sort(new FieldSortBuilder(ENTITY_ID).order(SortOrder.DESC));
		searchSourceBuilder.sort(new FieldSortBuilder(STREAM_SOURCE).order(SortOrder.DESC));
	}

	public static SearchSourceBuilder getSourceBuilder() {
		SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
		searchSourceBuilder.timeout(TimeValue.timeValueMillis(socketTimeout));
		return searchSourceBuilder;
	}

	public static QueryBuilder getQueryBuilderForDesiredParameters(final List<String> indices,
			final SearchContext searchContext, final String reqId, final String parentQueryTypeIdentifier) {
		if (searchContext == null) {
			log.error("SearchContext formed is null for reqId : {} and entityId : {}", reqId,
					searchContext.getEntityId());
			throw ExceptionFactory.getException(PANAROMA_SERVICE, INTERNAL_SERVER_ERROR)
				.setTag("getQueryBuilderForDesiredParameters");
		}
		BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
		Method queryTypeMethod = null;
		try {
			queryTypeMethod = BoolQueryBuilder.class.getDeclaredMethod(parentQueryTypeIdentifier, QueryBuilder.class);

			// Applying txnDate Range query if date range filter is not applied
			if (StringUtils.isBlank(searchContext.getEsDateRange())) {
				queryTypeMethod.invoke(queryBuilder, getQueryBuilder(SearchCriteriaEnum.txnDateRange, searchContext,
						queryBuilder, searchContext.getFromDate(), searchContext.getToDate()));
			}
		}
		catch (Exception e) {
			log.error(
					"exception in extracting/invoking method : {} in BoolQueryBuilder with Exception: {}, "
							+ "reqId: {} and entityId : {}",
					parentQueryTypeIdentifier, CommonsUtility.exceptionFormatter(e), reqId,
					searchContext.getEntityId());
			throw ExceptionFactory.getException(PANAROMA_SERVICE, QUERY_EXCEPTION);
		}
		Iterator<Pair<SearchCriteriaEnum, Object>> searchContextIterator = searchContext.iterator();
		try {
			while (searchContextIterator.hasNext()) {
				Pair<SearchCriteriaEnum, Object> pair = searchContextIterator.next();
				if (pair == null || pair.getKey().isExcluded()) {
					// Condition for getting query from filter handler
					if (pair != null && pair.getKey().isQueryFromFilter() && pair.getValue() != null
							&& StringUtils.isNotEmpty(pair.getKey().getFilterTypeIdentifier())) {
						getQueryFromFilterHandler(searchContext, queryBuilder, queryTypeMethod,
								pair.getKey().getFilterTypeIdentifier(), reqId, indices);
					}
					continue;
				}

				if (ObjectUtils.isNotEmpty(pair.getValue())) {
					QueryBuilder queryBuilderForCurrentParameter = getQueryBuilder(pair.getKey(), searchContext,
							queryBuilder, pair.getValue());
					if (queryBuilderForCurrentParameter == null) {
						continue;
					}
					log.debug("query created key: {}, value :{}", pair.getKey(), pair.getValue());
					if (SearchCriteriaEnum.autoCompleteFinalQuery.equals(pair.getKey())) {
						queryBuilder.must(queryBuilderForCurrentParameter);
					}
					else {
						queryBuilder.filter(queryBuilderForCurrentParameter);
					}
				}
			}
			// handling searchContext show bankData
			handleShowBankDataQuery(searchContext.isShowBankData(), queryBuilder, queryTypeMethod);
		}
		catch (Exception e) {
			log.error("exception while iterating over search context and generating query, reqId: {}, "
					+ "entityId : {}, {}", reqId, searchContext.getEntityId(), e.getMessage());
			throw ExceptionFactory.getException(PANAROMA_SERVICE, QUERY_EXCEPTION);
		}

		return queryBuilder;
	}

	// this function is used to iterate over nested objects
	private static QueryBuilder formQueryUsingIterator(final Iterator<Pair<SearchCriteriaEnum, Object>> iterator,
			final SearchContext searchContext) {
		BoolQueryBuilder boolShouldQueryBuilder = QueryBuilders.boolQuery();
		try {
			if (iterator == null) {
				throw new Exception("Iterator is null");
			}
			while (iterator.hasNext()) {
				Pair<SearchCriteriaEnum, Object> pair = iterator.next();
				if (pair == null || pair.getKey().isExcluded()) {
					continue;
				}

				if (pair.getValue() != null) {
					QueryBuilder queryBuilderForCurrentParameter = getQueryBuilder(pair.getKey(), searchContext,
							boolShouldQueryBuilder, pair.getValue());
					log.debug("query created key: {}, value :{}", pair.getKey(), pair.getValue());
					boolShouldQueryBuilder.should(queryBuilderForCurrentParameter);
				}
			}
			boolShouldQueryBuilder.minimumShouldMatch(1);
		}
		catch (Exception e) {
			log.error("exception while iterating over search context and generating query, " + "entityId : {}",
					searchContext.getEntityId());
			throw ExceptionFactory.getException(PANAROMA_SERVICE, QUERY_EXCEPTION);
		}
		return boolShouldQueryBuilder;
	}

	private static QueryBuilder getQueryBuilder(final SearchCriteriaEnum key, final SearchContext searchContext,
			final BoolQueryBuilder parentQueryBuilder, final Object... value) {
		QueryBuilder queryBuilder = null;
		BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
		log.debug("trying to create query for key :{}, value :{}", key, value);
		Object searchValue = null;
		if (value != null && value.length >= 1) {
			searchValue = value[0];
		}
		else {
			log.error("exception while extracting value from array for ES query field : {}",
					SearchContextToESMappings.get(key.toString()));
			throw ExceptionFactory.getException(PANAROMA_SERVICE, QUERY_EXCEPTION);
		}

		switch (key.getQueryType()) {
			case RANGE:
				if (value.length == 2) {
					queryBuilder = QueryBuilders.rangeQuery(SearchContextToESMappings.get(key.toString()))
						.gte(value[0])
						.lte(value[1]);
				}
				else if (value.length == 1 && key.equals(SearchCriteriaEnum.fromUpdatedDate)) {
					queryBuilder = QueryBuilders.rangeQuery(SearchContextToESMappings.get(key.toString()))
						.gte(value[0]);
				}
				else if (value.length == 1 && key.equals(SearchCriteriaEnum.updatedDateRangeValue)) {
					String[] dates = value[0].toString().split(",");
					queryBuilder = QueryBuilders.rangeQuery(SearchContextToESMappings.get(key.toString()))
						.gte(dates[0])
						.lte(dates[1]);
				}
				else {
					log.error("exception while extracting value from array for ES query field : {}",
							SearchContextToESMappings.get(key.toString()));
					throw ExceptionFactory.getException(PANAROMA_SERVICE, QUERY_EXCEPTION);
				}
				break;
			case TERM:
				queryBuilder = QueryBuilders.termQuery(getQueryFields(key, searchContext), searchValue);
				break;
			case TERMS:
				queryBuilder = QueryBuilders.termsQuery(getQueryFields(key, searchContext),
						((String) searchValue).split(","));
				break;
			case NESTED:
				queryBuilder = QueryBuilders.nestedQuery(key.getNestedPath(),
						QueryBuilders.termQuery(getQueryFields(key, searchContext), searchValue), ScoreMode.None);
				break;
			case REGEXP_NESTED:
				String regexp = ".*" + searchValue + ".*";
				queryBuilder = QueryBuilders.nestedQuery(key.getNestedPath(),
						QueryBuilders.regexpQuery(getQueryFields(key, searchContext), regexp), ScoreMode.None);
				break;

			case MULTI_VALUED_SEARCH_NESTED:
				for (String val : ((String) searchValue).split(",")) {
					boolQueryBuilder.should(QueryBuilders.termQuery(getQueryFields(key, searchContext), val));
				}
				boolQueryBuilder.minimumShouldMatch(1);
				queryBuilder = QueryBuilders.nestedQuery(key.getNestedPath(), boolQueryBuilder, ScoreMode.None);
				break;

			case MULTI_VALUED_SEARCH:
				for (String val : ((String) searchValue).split(",")) {
					boolQueryBuilder.should(QueryBuilders.termQuery(getQueryFields(key, searchContext), val));
				}
				boolQueryBuilder.minimumShouldMatch(1);
				queryBuilder = boolQueryBuilder;
				break;

			case MULTI_MATCH_SEARCH:
				queryBuilder = QueryBuilders.multiMatchQuery(searchValue, getQueryFields(key, searchContext).split(","))
					.operator(Operator.AND)
					.type(MultiMatchQueryBuilder.Type.CROSS_FIELDS)
					.fuzzyTranspositions(Boolean.FALSE)
					.autoGenerateSynonymsPhraseQuery(false);
				break;

			case MULTI_MATCH_AUTOCOMPLETE_EXACT_MATCH_BOOST:
				createMultiMatchAutocompleteExactMatchBoostQuery(searchContext, searchValue, parentQueryBuilder);
				queryBuilder = null;
				break;

			case MULTI_MATCH_AUTOCOMPLETE:
				if (configurablePropertiesHolder.getProperty(FUZZY_SERACH_REQUIRED, Boolean.class)) {
					queryBuilder = QueryBuilders
						.multiMatchQuery(searchValue,
								SearchIntelligentUtility.getFieldsForQuery(String.valueOf(searchValue)))
						.operator(Operator.AND)
						.fuzziness(Fuzziness.ONE)
						.fuzzyTranspositions(Boolean.FALSE)
						.autoGenerateSynonymsPhraseQuery(false)
						.maxExpansions(110);
				}
				else {
					queryBuilder = QueryBuilders
						.multiMatchQuery(searchValue,
								SearchIntelligentUtility.getFieldsForQuery(String.valueOf(searchValue)))
						.operator(Operator.AND)
						.fuzziness(Fuzziness.ZERO)
						.fuzzyTranspositions(Boolean.FALSE)
						.autoGenerateSynonymsPhraseQuery(false)
						.maxExpansions(110);
				}
				break;

			case MULTI_MATCH_OVER_KEYWORD:
				String queryFields = getQueryFields(key, searchContext);
				String[] queryFieldsArray = queryFields.split(COMMA);
				queryBuilder = QueryBuilders.multiMatchQuery(searchValue, queryFieldsArray)
					.operator(Operator.AND)
					.fuzziness(Fuzziness.ZERO)
					.fuzzyTranspositions(false)
					.autoGenerateSynonymsPhraseQuery(false);
				break;

			case "multiTermsSearch":
				String[] values = ((String) searchValue).split(",");
				queryBuilder = QueryBuilders.termsQuery(SearchContextToESMappings.get(key.toString()), values);
				break;

			case MULTI_MONTH_SEARCH:
				String[] dateRanges = ((String) searchValue).split(COMMA);

				for (String dateRange : dateRanges) {
					List<String> splitDateRange = Arrays.asList(dateRange.split(HYPHEN));
					QueryBuilder rangeQueryBuilder = QueryBuilders
						.rangeQuery(SearchContextToESMappings.get(SearchCriteriaEnum.txnDateRange.toString()))
						.gte(Long.parseLong(splitDateRange.get(0)))
						.lte(Long.parseLong(splitDateRange.get(1)));
					boolQueryBuilder.should(rangeQueryBuilder);
				}
				boolQueryBuilder.minimumShouldMatch(1);
				queryBuilder = boolQueryBuilder;
				break;

			case "should":
				Iterator<Pair<SearchCriteriaEnum, Object>> iterator = null;
				if (searchValue instanceof SearchCustomContext) {
					if (searchValue.equals(new SearchCustomContext())) {
						break;
					}
					iterator = ((SearchCustomContext) searchValue).iterator();
				}

				if (searchValue instanceof SearchTxnCategory) {
					if (searchValue.equals(new SearchTxnCategory())) {
						break;
					}
					iterator = ((SearchTxnCategory) searchValue).iterator();
				}
				queryBuilder = formQueryUsingIterator(iterator, searchContext);
				break;

			default:
				log.error("unexpected query type for query creation. key : {}", key);
				throw new IllegalStateException("Unexpected value: " + key.getQueryType());
		}
		return queryBuilder;
	}

	private static void createMultiMatchAutocompleteExactMatchBoostQuery(final SearchContext searchContext,
			final Object searchValue, final BoolQueryBuilder parentQueryBuilder) {
		if (configurablePropertiesHolder.getProperty(FUZZY_SERACH_REQUIRED, Boolean.class)) {
			// check for fuzziness
			boolean fuzzySearchRequired = SearchIntelligentUtility.isFuzzinessReq(searchContext);
			/*
			 * @param1 -> searchValue contains token created from query. Example : For
			 * query : preet -> "pre eet" is searchValue.
			 *
			 * @param2 -> withFuzzinessOne -> true (fuzziness one req)
			 *
			 * @param3 -> isNgramFieldReq -> true (as searchValue contains token)
			 */
			if (fuzzySearchRequired) {
				QueryBuilder fuzzyQuery = QueryBuilders
					.multiMatchQuery(searchValue,
							SearchIntelligentUtility.getNgramFieldsForQuery(String.valueOf(searchValue)))
					.operator(Operator.AND)
					.fuzziness(Fuzziness.ONE)
					.fuzzyTranspositions(Boolean.FALSE)
					.autoGenerateSynonymsPhraseQuery(false)
					.maxExpansions(110);
				parentQueryBuilder.should(fuzzyQuery);
			}
		}
		/*
		 * @param1 -> searchValue contains token created from query. Example : For query :
		 * preet -> "pre eet" is searchValue.
		 *
		 * @param2 -> withFuzzinessOne -> false (fuzziness zero req)
		 *
		 * @param3 -> isNgramFieldReq -> true (as searchValue contains token)
		 */
		QueryBuilder fuzzyQuery = QueryBuilders
			.multiMatchQuery(searchValue, SearchIntelligentUtility.getNgramFieldsForQuery(String.valueOf(searchValue)))
			.operator(Operator.AND)
			.fuzziness(Fuzziness.ZERO)
			.fuzzyTranspositions(Boolean.FALSE)
			.autoGenerateSynonymsPhraseQuery(false)
			.maxExpansions(110);
		parentQueryBuilder.should(fuzzyQuery);

		/*
		 * First get the list of Fields on which we'll do keyword searching. but exclude
		 * fields where we are already doing zero fuzziness searching.
		 *
		 * @param1 -> searchValue contains token created from query. Example : For query :
		 * preet -> "preet" is searchValue.
		 *
		 * @param2 -> withFuzzinessOne -> false (fuzziness one req)
		 *
		 * @param3 -> isNgramFieldReq -> false (as searchValue exact query entered by
		 * user)
		 */

		fuzzyQuery = QueryBuilders
			.multiMatchQuery(searchContext.getAutoCompleteQuery(),
					SearchIntelligentUtility
						.getFilteredFieldsForQuery(String.valueOf(searchContext.getAutoCompleteQuery())))
			.operator(Operator.AND)
			.fuzziness(Fuzziness.ZERO)
			.fuzzyTranspositions(Boolean.FALSE)
			.autoGenerateSynonymsPhraseQuery(false)
			.maxExpansions(110);
		parentQueryBuilder.should(fuzzyQuery);
		parentQueryBuilder.minimumShouldMatch(1);
	}

	private static String getQueryFields(final SearchCriteriaEnum searchCriteriaEnum,
			final SearchContext searchContext) {

		try {
			String queryFields = null;
			if (StringUtils.isNotBlank(searchCriteriaEnum.getFieldNameOfSearchContextForQueryFields())) {
				Field field = SearchContext.class
					.getDeclaredField(searchCriteriaEnum.getFieldNameOfSearchContextForQueryFields());
				field.setAccessible(true);
				queryFields = (String) field.get(searchContext);
			}
			if (StringUtils.isNotBlank(queryFields)) {
				return queryFields;
			}
			// QueryFields not defined then fetching from constant map
			queryFields = SearchContextToESMappings.get(searchCriteriaEnum.toString());
			return queryFields;
		}
		catch (Exception e) {
			log.error("Exception while getting query fields of field : {}, Exception : {}",
					searchCriteriaEnum.toString(), CommonsUtility.exceptionFormatter(e));
			throw ExceptionFactory.getException(PANAROMA_SERVICE, QUERY_EXCEPTION);
		}
	}

	public static HighlightBuilder getEsQueryHighLighter(final String searchFieldsToHighLight,
			final String highlightType, final String[] postTags, final String[] preTags) {
		HighlightBuilder highlightBuilder = new HighlightBuilder().postTags(postTags)
			.preTags(preTags)
			.highlighterType(highlightType);
		String[] searchFieldsToHighLightList = searchFieldsToHighLight.split(",");
		for (String fieldName : searchFieldsToHighLightList) {
			highlightBuilder.field(fieldName);
		}
		return highlightBuilder;
	}

	public static HighlightBuilder getEsQueryHighLighter(final String[] searchFieldsToHighLight,
			final String highlightType, final String[] postTags, final String[] preTags) {
		HighlightBuilder highlightBuilder = new HighlightBuilder().postTags(postTags)
			.preTags(preTags)
			.highlighterType(highlightType);
		for (String fieldName : searchFieldsToHighLight) {
			highlightBuilder.field(fieldName);
		}
		return highlightBuilder;
	}

	private static void handleAccountTypeFilterQuery(final SearchContext searchContext,
			final BoolQueryBuilder queryBuilder, final Method queryTypeMethod) {
		Iterator accountTypeFilterSearchContextIterator = searchContext.getAccountTypeFilterSearchContext().iterator();
		try {
			BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
			while (accountTypeFilterSearchContextIterator.hasNext()) {
				BoolQueryBuilder boolQueryBuilderMust = QueryBuilders.boolQuery();
				Pair<SearchCriteriaEnum, Object> pair = (Pair<SearchCriteriaEnum, Object>) accountTypeFilterSearchContextIterator
					.next();
				if (pair == null || pair.getKey().isExcluded()) {
					continue;
				}
				SearchCriteriaEnum key = pair.getKey();
				Object value = pair.getValue();
				if (pair.getValue() != null) {
					switch (key.getQueryType()) {
						case "nested":
							boolQueryBuilderMust
								.must(QueryBuilders.termQuery(SearchContextToESMappings.get(key.toString()), value));
							break;
						case "multiValuedSearchNested":
							BoolQueryBuilder boolQueryBuilderMultiValueShould = QueryBuilders.boolQuery();
							for (String val : ((String) value).split(",")) {
								boolQueryBuilderMultiValueShould.should(
										QueryBuilders.termQuery(SearchContextToESMappings.get(key.toString()), val));
							}
							boolQueryBuilderMultiValueShould.minimumShouldMatch(1);
							boolQueryBuilderMust.must(boolQueryBuilderMultiValueShould);
							break;
						default:
							log.error("unexpected query type for query creation. key : {}", key);
							throw new IllegalStateException("Unexpected value: " + key.getQueryType());
					}
					if (SearchCriteriaEnum.ifsc.equals(key) || SearchCriteriaEnum.esPaymentSystem.equals(key)) {
						boolQueryBuilderMust.must(QueryBuilders.termQuery(
								SearchContextToESMappings.get(SearchCriteriaEnum.esSecondPartyId.toString()),
								searchContext.getEntityId()));
					}
					boolQueryBuilder.should(boolQueryBuilderMust);
				}
			}
			QueryBuilder queryBuilderForCurrentParameter = QueryBuilders.nestedQuery(
					SearchCriteriaEnum.accountTypeFilterSearchContext.getNestedPath(), boolQueryBuilder,
					ScoreMode.None);
			queryTypeMethod.invoke(queryBuilder, queryBuilderForCurrentParameter);
		}
		catch (IllegalAccessException | InvocationTargetException | IllegalArgumentException e) {
			log.error("exception while invoking queryTypeMethod : {}", queryTypeMethod);
			throw ExceptionFactory.getException(PANAROMA_SERVICE, QUERY_EXCEPTION);
		}
		catch (Exception e) {
			log.error("exception while iterating over search context and generating query");
			throw ExceptionFactory.getException(PANAROMA_SERVICE, QUERY_EXCEPTION);
		}
	}

	private static void getQueryFromFilterHandler(final SearchContext searchContext,
			final BoolQueryBuilder queryBuilder, final Method queryTypeMethod, final String filterType,
			final String reqId, final List<String> indices) {
		try {
			// FiltersFactory filtersFactory = BeanUtil.getBean(FiltersFactory.class);
			IFilterType filterHandler = filtersFactory.getFilterHandlerByFilterType(filterType);

			if (filterHandler == null) {
				log.error("No Filter Handler found for this filterType : {}", filterType);
				throw ExceptionFactory.getException(PANAROMA_SERVICE, QUERY_EXCEPTION);
			}

			List<QueryBuilder> filterQueryList = filterHandler.getQueryForFilter(searchContext, indices);

			for (QueryBuilder filterQuery : filterQueryList) {
				queryTypeMethod.invoke(queryBuilder, filterQuery);
			}
		}
		catch (IllegalAccessException | InvocationTargetException e) {
			log.error("exception while invoking queryTypeMethod : {}, filterHandler: {}, reqId: {}, Exception: {}",
					queryTypeMethod, filterType, reqId, CommonsUtility.exceptionFormatter(e));
			throw ExceptionFactory.getException(PANAROMA_SERVICE, QUERY_EXCEPTION);
		}
		catch (Exception e) {
			log.error("Exception Occurred while generating query for filter: {}, reqId: {}, Exception: {}", filterType,
					reqId, CommonsUtility.exceptionFormatter(e));
			throw ExceptionFactory.getException(PANAROMA_SERVICE, QUERY_EXCEPTION);
		}
	}

	private static void handleShowBankDataQuery(final boolean showBankData, final BoolQueryBuilder queryBuilder,
			final Method queryTypeMethod) {
		// search context show bank data handling separately
		SearchCriteriaEnum searchCriteriaEnum = SearchCriteriaEnum.showBankData;
		String esMapping = SearchContextToESMappings.get(searchCriteriaEnum.toString());
		if (!showBankData) {
			BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
			boolQueryBuilder.should(QueryBuilders.termQuery(esMapping, false));
			BoolQueryBuilder mustNotBoolQueryBuilder = QueryBuilders.boolQuery();
			boolQueryBuilder.should(mustNotBoolQueryBuilder.mustNot(QueryBuilders.existsQuery(esMapping)));
			boolQueryBuilder.minimumShouldMatch(1);
			try {
				queryTypeMethod.invoke(queryBuilder, boolQueryBuilder);
			}
			catch (IllegalAccessException | InvocationTargetException | IllegalArgumentException e) {
				log.error("exception while invoking queryTypeMethod : {}", queryTypeMethod);
				throw ExceptionFactory.getException(PANAROMA_SERVICE, QUERY_EXCEPTION);
			}
		}
	}

}
