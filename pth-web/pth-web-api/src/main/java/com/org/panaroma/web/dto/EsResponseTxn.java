package com.org.panaroma.web.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.org.panaroma.commons.dto.Currency;
import com.org.panaroma.commons.utils.JsonUtils;
import com.org.panaroma.web.dto.detailAPI.RepeatPayment;
import com.org.panaroma.web.dto.detailAPI.detailResponseV2.CtaNode;
import com.org.panaroma.web.enums.ListingResponseMappingEnum;
import java.util.List;
import java.util.Map;
import lombok.Data;

@Data
public class EsResponseTxn {

	String dateLabel;

	String timeLabel;

	String amount;

	String txnIndicator;

	Currency currency;

	List<UserInstrumentLogos> userInstrumentLogos;

	SecondPartyInfo secondPartyInfo;

	String status;

	String statusKey;

	String narration;

	List<String> txnTags;

	boolean tagEnable;

	String txnId;

	String sourceTxnId;

	Long docUpdatedDate;

	String streamSource;

	String streamSourceKey;

	String merchantCategory;

	/**
	 * Need to show all sub-wallet txns in wallet passbook. There will be txns with
	 * multiple sub-wallets involved, so we can not show closing balance for each
	 * sub-wallet used. So, we would stop showing closing balance in wallet passbook, will
	 * instead show instrument narration with logo same as UTH passbook.
	 */
	// String closingBalance;

	// when we need to mask amount, set true
	boolean maskAmount;

	String avenueOrderId;

	// if this is null, then app itself will derive this
	String userInstrumentNarration;

	// this will be used by app team whenever userInstrumentNarration value is not null.
	List<UserInstrumentLogos> userInstrumentLogosV2;

	private Map<String, CtaNode> ctasMap; // (Key -> Value) ctaType -> ctaNode

	private String txnAmountColour;

	// Added txnDate in epoch. which currently will be used for detail routing to Dc
	// cluster
	private Long txnDate;

	// This will be shown in front of time
	private String dateTimeLabel;

	@JsonIgnore
	private ListingResponseMappingEnum listingResponseMappingEnum;

	// new object added to support PTL use case
	private List<InstrumentInfo> userInstrumentInfo;

	// new object to support on-device filter use case
	private SearchFilters searchFilters;

	@JsonInclude(JsonInclude.Include.NON_NULL)
	private List<String> searchAbleStrings;

	@JsonInclude(JsonInclude.Include.NON_NULL)
	private String mccCode;

	private Boolean isHiddenTxn;

	private RepeatPayment repeatPayment;

	@Override
	public String toString() {
		return JsonUtils.toJson(this);
	}

}
