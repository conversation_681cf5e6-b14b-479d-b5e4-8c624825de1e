package com.org.panaroma.web.utility.configurablePropertyUtility;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.org.panaroma.commons.constants.ConfigPropertiesEnum;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.commons.utils.DateTimeUtility;
import com.org.panaroma.commons.utils.MdcUtility;
import com.org.panaroma.commons.utils.rollout.strategy.RolloutStrategyHelper;
import com.org.panaroma.web.exceptionhandler.ExceptionFactory;
import com.org.panaroma.web.utility.configurablePropertyUtility.sources.BoPanelPropertySource;
import java.util.*;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.WebApiV1Constants.APP_CACHE_INVALIDATE_VERSION;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.WebApiV1Constants.APP_CACHE_LATEST_INVALIDATE_VERSION;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.WebApiV1Constants.DEFAULT_ELIGIBLE_PROPERTIES_FOR_NUMBER_OF_DAYS_CONVERSION;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.WebApiV1Constants.SUFFIX_FOR_NUMBER_OF_DAYS_PROPERTIES;
import static com.org.panaroma.commons.constants.WebConstants.PANAROMA_SERVICE;
import static com.org.panaroma.web.exceptionhandler.ErrorCodeConstants.INTERNAL_SERVER_ERROR;

@Component
@Log4j2
public class ConfigurablePropertiesHolder {

	private final List<PropertySource> propertySources;

	private final ObjectMapper objectMapper;

	private final RolloutStrategyHelper rolloutStrategyHelper;

	private Environment environment;

	public ConfigurablePropertiesHolder(final BoPanelPropertySource boPanelSource,
			final RolloutStrategyHelper rolloutStrategyHelper, final Environment environment) {
		this.propertySources = Arrays.asList(boPanelSource);
		this.objectMapper = new ObjectMapper();
		this.rolloutStrategyHelper = rolloutStrategyHelper;
		this.environment = environment;
	}

	public <T> T getProperty(final ConfigPropertiesEnum config, final Class<T> type) {
		/*
		 * As per similar method in existing ConfigurablePropertiesHolder class, it only
		 * fetches value from BO Panel & used default value in case no value is found. It
		 * doesn't try to fetch value from other sources like env. But as per this new
		 * code, it tries to fetch value from all sources & after that only uses the
		 * default value
		 */

		/*
		 * Here I am explicitly converting the default value to the required type because
		 * config.getDefaultValue() is always String but type can be something else as
		 * well
		 */
		T defaultValue = convertToType(config.getDefaultValue(), type);
		return getProperty(config.getKey(), type, defaultValue);
	}

	public <T> T getProperty(final String propertyName, final Class<T> type) {
		return getProperty(propertyName, type, null);
	}

	public <T> T getPropertyWithDefaultValue(final String propertyName, final Class<T> type, final T defaultValue) {
		try {
			return getProperty(propertyName, type, defaultValue);
		}
		catch (NoSuchElementException e) {
			return null;
		}
	}

	private <T> T getProperty(final String propertyName, final Class<T> type, final T defaultValue) {
		if (StringUtils.isBlank(propertyName)) {
			log.error("No property name provided to fetch the property value.");
			throw ExceptionFactory.getException(PANAROMA_SERVICE, INTERNAL_SERVER_ERROR);
		}
		T finalPropertyValue = null;
		try {
			/*
			 * propertySources is a list of PropertySource objects. As of now there is
			 * only one PropertySource i.e. BoPanelPropertySource. The code is written
			 * keeping in mind that there can be other PropertySource implementations as
			 * well in future
			 */
			finalPropertyValue = propertySources.stream()
				.sorted(Comparator.comparingInt(PropertySource::getPriority)) // Sort by
																				// priority
				// (PropertySource with lowest value will be evaluated first)
				.map(source -> source.getProperty(propertyName))
				.filter(Optional::isPresent)
				.map(Optional::get)
				.filter(value -> !value.isBlank())
				.map(value -> convertToType(value, type))
				.filter(Objects::nonNull)
				.findFirst()
				.orElseGet(() -> {
					T valueFromEnv = environment.getProperty(propertyName, type);
					return valueFromEnv;
				});

			T overriddenPropertyValue = null;

			try {
				if (DEFAULT_ELIGIBLE_PROPERTIES_FOR_NUMBER_OF_DAYS_CONVERSION.contains(propertyName)) {
					Integer numberOfDays = getProperty(propertyName + SUFFIX_FOR_NUMBER_OF_DAYS_PROPERTIES,
							Integer.class, null);
					if (Objects.nonNull(numberOfDays) && numberOfDays > 0) {
						overriddenPropertyValue = convertToType(
								DateTimeUtility.subtractDaysToListingFormattedDateFromNow(numberOfDays), type);
					}
				}

				if (APP_CACHE_INVALIDATE_VERSION.equals(propertyName)) {
					if (rolloutStrategyHelper.isUserWhiteListed("newInvalidationVersionApplicable",
							MdcUtility.getUserId())) {
						overriddenPropertyValue = (T) getProperty(APP_CACHE_LATEST_INVALIDATE_VERSION, Integer.class,
								null);
					}
				}
			}
			catch (Exception e) {
				log.error("Exception while overriding property value for propertyName : {}, Exception : {}",
						propertyName, CommonsUtility.exceptionFormatter(e));
			}

			if (Objects.nonNull(overriddenPropertyValue)) {
				finalPropertyValue = overriddenPropertyValue;
			}

			if (Objects.isNull(finalPropertyValue) && Objects.nonNull(defaultValue)) {
				finalPropertyValue = defaultValue;
			}

		}
		catch (Exception exception) {
			if (Objects.nonNull(defaultValue)) {
				log.error(
						"Error while fetching property {} of type {} with defaultValue : {}."
								+ " Proceeding with using defaultValue. Exception : {}",
						propertyName, type, defaultValue, exception.getMessage());
				finalPropertyValue = defaultValue;
			}
		}
		if (Objects.isNull(finalPropertyValue)) {
			log.error("No property fetched for propertyName {} of type {} when defaultValue provided is {}",
					propertyName, type, defaultValue);
			throw new NoSuchElementException("Requested property " + propertyName + " not found on BO Panel/env");
		}
		log.debug("propertyName : {}, propertyValue : {}, type : {}", propertyName, finalPropertyValue, type);
		return finalPropertyValue;
	}

	public <T> Map<String, T> getPropertiesWithPrefix(final String prefix, final Class<T> type) {
		if (StringUtils.isBlank(prefix)) {
			log.error("No prefix provided to fetch the property values starting with particular prefix.");
			throw ExceptionFactory.getException(PANAROMA_SERVICE, INTERNAL_SERVER_ERROR);
		}

		return propertySources.stream()
			.sorted(Comparator.comparingInt(PropertySource::getPriority)) // Sort by
																			// priority
			// (PropertySource with lowest value will be evaluated first)
			.map(source -> source.getPropertiesWithPrefix(prefix, type))
			.filter(map -> !CollectionUtils.isEmpty(map))
			.findFirst()
			.orElseGet(() -> Collections.<String, T>emptyMap());
	}

	private <T> T convertToType(final String value, final Class<T> type) {
		try {
			if (value == null) {
				return null;
			}
			if (type == String.class) {
				return (T) value;
			}
			if (type == Boolean.class || type == boolean.class) {
				return (T) Boolean.valueOf(value);
			}
			if (type == Integer.class || type == int.class) {
				return (T) Integer.valueOf(value);
			}
			if (type == Long.class || type == long.class) {
				return (T) Long.valueOf(value);
			}
			if (type == Double.class || type == double.class) {
				return (T) Double.valueOf(value);
			}
			return objectMapper.readValue(value, type);
		}
		catch (Exception e) {
			log.error("Error converting value {} to type {}. Exception : {}", value, type, e.getMessage());
			return null;
		}
	}

}