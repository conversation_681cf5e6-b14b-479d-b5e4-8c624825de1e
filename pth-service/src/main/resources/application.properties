server.port     = 8080
build.timestamp = @timestamp@
app.name        = @project.artifactId@
app.version     = @project.version@
git.commit.id   = 122
git.branch      = master
logging.config  = classpath:log4j2.json
min-amount-limit-for-amount-range-filter = 0
max-amount-limit-for-amount-range-filter = 1000000
#Compression Properties
server.compression.enabled = true
#current - 1 KB Default - 2 KB
server.compression.min-response-size = 1024
server.compression.mime-types = application/json,application/json;charset=UTF-8,text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/xml

server.tomcat.max-connections                      = 1000
server.tomcat.max-threads                          = 500
server.tomcat.threads.max                          = 500

elastic-search-index                               = payment_history_alias
elastic-search-index-prefix = payment-history-

es-socket-timeout                     = 2000
es-max-retry-timeout                  = 2000
es-connect-timeout                    = 1000
es-connect-request-timeout            = 1000

es-host-list                          = 127.0.0.1
es-port                               = 9200

#V2 ES Configuration
es-v2-host-list                                    = 127.0.0.1
es-v2-port                                         = 9200

#Mandate es configuration
mandate-es-host-list = uth-es-app-mandate-nlb-ba8f1f4864d65bfe.elb.ap-south-1.amazonaws.com



server.tomcat.accesslog.enabled                    = true
server.tomcat.accesslog.pattern                    = [%{yyyy-MM-dd HH:mm:ss.SSS}t] %h \"%r\" %s %b %Dms \"%{Referer}i\" \"%{User-agent}i\" \"%{requestIdLogging}r\"
server.tomcat.accesslog.directory                  = /log/payment-transaction-history-service
server.tomcat.accesslog.prefix                     = ${HOSTNAME}_access
server.tomcat.accesslog.rotate                     = true
server.tomcat.accesslog.rename-on-rotate           = true
server.tomcat.accesslog.file-date-format           = .yyyy-MM-dd
server.tomcat.accesslog.buffered                   = false
server.tomcat.accesslog.request-attributes-enabled = true
server.tomcat.accesslog.maxDays                    = 3

user.tag.agg.data.table = user_tag_agg_data-pth
txn.tags.table                                 = txn_tag_data-pth
spend_analytics.status.table = analytic_status_data-pth
shedlock.table = Shedlock
user.tags.table                                = user_tag_data-pth
userId.updatedTimeStamp.index                  = userId-updatedTimeStamp-index
sysyem.tags.table                              = system_tag_data-pth
tagging.max.tags.count.per.txn                              = 1
tagging.tag.max.length                                     = 30
tagging.tag.min.length                                     = 3
tagging.user.tags.limit                                     = 100
tagging.user.tags.count.in.suggested.tags                  = 2
tagging.suggested.tags.max.count                          = 26
tagging.search.supported.tags.max.count                   = 100
tagging.auto.tagging.enabled                    = false

tagging.use.secondary.es.for.user.tags             = true
tagging.total.txns.for.creating.user.tags.from.es  = 150
tagging.no.of.days.older.txns.for.user.tags.from.es = 30


jwt.varification.enable = true
uth.upi.txn.history.secret.key = 83738hbfjfbfjb2565265lkldkldk
pth.upi.switch.secret.key = xyz

#Ocl OAuth
external-integration-service-http-url = http://external-integration.default:80
ocl.oauth.service.base.url                = https://accounts-plus-proxy.paytm.com
ocl.oauth.client.id                       = upi-pth-staging
ocl.oauth.client.secret                   = VWbL9i6sLxDUCpdmi7IekZzuMImcggwA

use-bank-oauth                            = true

#bank-oauth
#bank.oauth.service.base.url                        = http://localhost:9091
bank.oauth.service.base.url                         = https://oauth-ite.orgk.com
bank.oauth.service.url.category                    = /bank-oauth/ext
bank.oauth.client.id                               = MIDDLEWARE_PAYMENT_HISTORY_BANKOAUTH_CONFIG_CLIENTID
bank.oauth.client.secret                           = MIDDLEWARE_PAYMENT_HISTORY_BANKOAUTH_CONFIG_CLIENTSECRET
bank.oauthClient.connectionProvider.maxConnections      = 500
bank.oauthClient.connectionProvider.acquireTimeout      = 45000
bank.oauthClient.connectionProvider.maxIdleTime         = 5
bank.oauth.client.connection.timeout                    = 2000
bank.oauth.read.timeout                                = 2000
bank.oauth.socket.timeout                               = 2000

base.url.for.merchant.logo = https://catalog-staging.paytm.com/images/
url.for.ondc.logo = https://assetscdn1.paytm.com/images/catalog/view_item/1519347/*************.png
ondc.verticalId = 204
cart.whitelistedMerchantVerticalIds = 204
cart.whitelisted.userIds.list.for.narration = -1

prometheus.explorer                                         = UNIFIED_HiSTORY_SERVICE
prometheus.hostname                                         = localhost
prometheus.port                                             = 8130

updated.index.month.list = payment-history-08-2020
index.name.prefix = payment-history                                             = 8130
upi-udir-index.name.prefix = uth-cst


oauth.token.evaluation.skip = false
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.data.elasticsearch.ElasticsearchDataAutoConfiguration

spend_analytics.source.table = user_analytics_month_agg_data-pth

envBased.jacocoSupport = true

#need to ask
analytic.first.page.spent.logo.url = https://static.orgk.com/uth/images/category-logo/Spend_Analytics_Money.png

spend.index.name.prefix = spend-history
spend-history-alias = spend_history_alias
#From Date Jan 2022 (MM-yyyy)
analytics-from-month = 01-2022
analytics-dataBreakUp-page-size = 20

static-bank-logo-base-url = https://tpap-logo.paytm.com/uth/images/bank-logo/
static-category-logo-base-url = https://tpap-logo.paytm.com/uth/images/category-logo/
static-status-logo-base-url = https://tpap-logo.paytm.com/uth/images/status-logo/
static-wallet-logo-base-url =  https://tpap-logo.paytm.com/uth/images/wallet-logo/
static-paytm-logo-base-url  =  https://tpap-logo.paytm.com/uth/images/paytm-logo/
static-upi-merchant-logo-base-url  =  https://tpap-logo.paytm.com/upi/images/merchant-logo/
static-merchant-logo-base-url  =  https://tpap-logo.paytm.com/uth/images/merchant-logo/
static-paymentMode-logo-base-url = https://tpap-logo.paytm.com/uth/images/payment-intiation-mode/
static-mandate-history-merchant-vpa-logo-base-url = https://pwebassets.paytm.com/ocl-upi/upi/images/merchant-logo/
generic-tag-logo = tags.png


map.of.uth.category.logo.url          = {\
                                            '1': 'https://tpap-logo.paytm.com/uth/images/category-logo/DAIRY_AND_GROCERIES.png',\
                                            '2': 'https://tpap-logo.paytm.com/uth/images/category-logo/SERVICES.png',\
                                            '3': 'https://tpap-logo.paytm.com/uth/images/category-logo/FUEL_AND_AUTOMOBILE.png',\
                                            '4': 'https://tpap-logo.paytm.com/uth/images/category-logo/Food_And_Beverages.png',\
                                            '5': 'https://tpap-logo.paytm.com/uth/images/category-logo/Shoppings.png',\
                                            '6': 'https://tpap-logo.paytm.com/uth/images/category-logo/PERSONAL_AND_HEALTH_CARE.png',\
                                            '7': 'https://tpap-logo.paytm.com/uth/images/category-logo/Taxi_And_Transportations.png',\
                                            '8': 'https://tpap-logo.paytm.com/uth/images/category-logo/TRAVEL.png',\
                                            '9': 'https://tpap-logo.paytm.com/uth/images/category-logo/RECHARGES_AND_BILL_PAYMENT.png',\
                                            '10': 'https://tpap-logo.paytm.com/uth/images/category-logo/ENTERTAINMENT.png',\
                                            '11': 'https://tpap-logo.paytm.com/uth/images/category-logo/Transfers.png',\
                                            '12': 'https://tpap-logo.paytm.com/uth/images/category-logo/DEVOTION.png',\
                                            '13': 'https://tpap-logo.paytm.com/uth/images/category-logo/Financial_Services.png',\
                                            '14': 'https://tpap-logo.paytm.com/uth/images/category-logo/Education.png',\
                                            '15': 'https://tpap-logo.paytm.com/uth/images/category-logo/Interest_Credited.png',\
                                            '16': 'https://tpap-logo.paytm.com/uth/images/category-logo/Cashback_Received.png',\
                                            '17': 'https://tpap-logo.paytm.com/uth/images/category-logo/Salary_Credited.png',\
                                            '18': 'https://tpap-logo.paytm.com/uth/images/category-logo/Others.png',\
                                            '19': 'https://tpap-logo.paytm.com/uth/images/category-logo/EDUCATIONAL.png',\
                                            '20': 'https://tpap-logo.paytm.com/uth/images/category-logo/CLOTHING_SHOES_JEWELRY.png',\
                                            '21': 'https://tpap-logo.paytm.com/uth/images/category-logo/SWEETS_BAKERY.png',\
                                            '22': 'https://tpap-logo.paytm.com/uth/images/category-logo/BOOKS_STATIONARY.png',\
                                            '23': 'https://tpap-logo.paytm.com/uth/images/category-logo/MOBILE_ACCESSORIES.png',\
                                            '24': 'https://tpap-logo.paytm.com/uth/images/category-logo/SMALL_SHOP.png',\
                                            '25': 'https://tpap-logo.paytm.com/uth/images/category-logo/ELECTRONICS.png',\
                                            '26': 'https://tpap-logo.paytm.com/uth/images/category-logo/INDUSTRIAL_SCIENTIFIC.png',\
                                            '27': 'https://tpap-logo.paytm.com/uth/images/category-logo/LEGAL_FINANCIAL.png',\
                                            '28': 'https://tpap-logo.paytm.com/uth/images/category-logo/LIFESTYLE.png',\
                                            '30': 'https://tpap-logo.paytm.com/uth/images/category-logo/gov_services.png',\
                                            '31': 'https://tpap-logo.paytm.com/uth/images/category-logo/software.png'\
                                        }


#time in seconds
cache.time.for.month.spent.data = 5
cache.time.for.months.agg.spent.data = 5
cache.time.for.analytics.agg.and.break.up = 5
cache.time.for.cst.bot.details = 10

# Cache time for rewind data at client end (In Hours)
cache.time.for.rewind.data = 24

kafka.target-v2-list[0].kafka-client-name               = USERID_FETCHER_KAFKA_CLIENT
kafka.target-v2-list[0].kafka-producer-key              = USERID_FETCHER_KAFKA_CLIENT_PRODUCER
kafka.target-v2-list[0].topic                           = middleware_transaction_history_userid_fetcher_data
kafka.target-v2-list[0].bootstrap-servers               = localhost:9092
kafka.target-v2-list[0].batch-size-bytes                = 3000
kafka.target-v2-list[0].linger-ms                       = 15
kafka.target-v2-list[0].request-timeout-ms              = 5000

kafka.target-v2-list[1].kafka-client-name               = SPEND_DOC_CREATOR_KAFKA_CLIENT_KEY
kafka.target-v2-list[1].kafka-producer-key              = SPEND_DOC_CREATOR_KAFKA_CLIENT_PRODUCER
kafka.target-v2-list[1].topic                           = uth_spend_user_kafka_config_data
kafka.target-v2-list[1].bootstrap-servers               = localhost:9092
kafka.target-v2-list[1].batch-size-bytes                = 3000
kafka.target-v2-list[1].linger-ms                       = 15
kafka.target-v2-list[1].request-timeout-ms              = 5000

kafka.target-v2-list[2].kafka-client-name               = spend_back_filling
kafka.target-v2-list[2].kafka-producer-key              = spend_back_filling_CLIENT_PRODUCER
kafka.target-v2-list[2].topic                           = uth_spend_user_kafka_config_data
kafka.target-v2-list[2].bootstrap-servers               = localhost:9092
kafka.target-v2-list[2].batch-size-bytes                = 3000
kafka.target-v2-list[2].linger-ms                       = 15
kafka.target-v2-list[2].request-timeout-ms              = 5000

kafka.target-v2-list[3].kafka-client-name               = SPEND_DOC_CREATOR_KAFKA_CLIENT_KEY_DC
kafka.target-v2-list[3].kafka-producer-key              = SPEND_DOC_CREATOR_KAFKA_CLIENT_PRODUCER_DC
kafka.target-v2-list[3].topic                           = middleware_transaction_history_back_fill_data
kafka.target-v2-list[3].bootstrap-servers               = localhost:9092
kafka.target-v2-list[3].batch-size-bytes                = 3000
kafka.target-v2-list[3].linger-ms                       = 15
kafka.target-v2-list[3].request-timeout-ms              = 5000

kafka.target-v2-list[4].kafka-client-name               = SPEND_DOC_CREATOR_KAFKA_CLIENT_KEY_DC_V1
kafka.target-v2-list[4].kafka-producer-key              = SPEND_DOC_CREATOR_KAFKA_CLIENT_PRODUCER_DC_V1
kafka.target-v2-list[4].topic                           = middleware_transaction_history_retry_back_fill_data
kafka.target-v2-list[4].bootstrap-servers               = localhost:9092
kafka.target-v2-list[4].batch-size-bytes                = 3000
kafka.target-v2-list[4].linger-ms                       = 15
kafka.target-v2-list[4].request-timeout-ms              = 5000

kafka.target-v2-list[5].kafka-client-name               = API_HIT_CLIENT_1
kafka.target-v2-list[5].kafka-producer-key              = API_HIT_CLIENT_1
kafka.target-v2-list[5].topic                           = uth_year_in_rewind_cache_v1
kafka.target-v2-list[5].bootstrap-servers               = localhost:9092
kafka.target-v2-list[5].batch-size-bytes                = 3000
kafka.target-v2-list[5].linger-ms                       = 15
kafka.target-v2-list[5].request-timeout-ms              = 5000

kafka.target-v2-list[6].kafka-client-name               = API_HIT_CLIENT_2
kafka.target-v2-list[6].kafka-producer-key              = API_HIT_CLIENT_2
kafka.target-v2-list[6].topic                           = uth_year_in_rewind_cache_v2
kafka.target-v2-list[6].bootstrap-servers               = localhost:9092
kafka.target-v2-list[6].batch-size-bytes                = 3000
kafka.target-v2-list[6].linger-ms                       = 15
kafka.target-v2-list[6].request-timeout-ms              = 5000

kafka.target-v2-list[7].kafka-client-name               = SAPI_HIT_CLIENT_3
kafka.target-v2-list[7].kafka-producer-key              = API_HIT_CLIENT_3
kafka.target-v2-list[7].topic                           = uth_year_in_rewind_cache_v3
kafka.target-v2-list[7].bootstrap-servers               = localhost:9092
kafka.target-v2-list[7].batch-size-bytes                = 3000
kafka.target-v2-list[7].linger-ms                       = 15
kafka.target-v2-list[7].request-timeout-ms              = 5000

kafka.target-v2-list[8].kafka-client-name               = SAPI_HIT_CLIENT_4
kafka.target-v2-list[8].kafka-producer-key              = API_HIT_CLIENT_4
kafka.target-v2-list[8].topic                           = uth_year_in_rewind_cache_v4
kafka.target-v2-list[8].bootstrap-servers               = localhost:9092
kafka.target-v2-list[8].batch-size-bytes                = 3000
kafka.target-v2-list[8].linger-ms                       = 15
kafka.target-v2-list[8].request-timeout-ms              = 5000

kafka.target-v2-list[11].kafka-client-name               = uthAnalyticsKafkaClient
kafka.target-v2-list[11].kafka-producer-key              = uthAnalyticsProducer
kafka.target-v2-list[11].topic                           = uth-dwh-ingestion-topic
kafka.target-v2-list[11].bootstrap-servers               = localhost:9092
kafka.target-v2-list[11].batch-size-bytes                = 3000
kafka.target-v2-list[11].linger-ms                       = 1
kafka.target-v2-list[11].request-timeout-ms              = 100

#1hour
analytic.config.cron.delay.in.milli = 900000
#15 min
analytic.config.cron.lock.at.most.in.milli = 900000

analytic.scheduler.enabled.for.whitelisted.users.only = true
whitelisted.custids.for.analytics = 123,456,**********

bank-passbook-migration-date = *************
uth.ppbl.txn.history.secret.key = ppbl-uth-sk8-key

rollout.config-list[0].percentage = 100
rollout.config-list[0].whitelisting-required = 1
rollout.config-list[0].user-list = 123
rollout.config-list[0].whitelisting-for = ext/v1/spend-analytics/monthly

rollout.config-list[1].percentage = 100
rollout.config-list[1].whitelisting-required = 1
rollout.config-list[1].user-list = 123
rollout.config-list[1].whitelisting-for = ext/v1/spend-analytics/data

rollout.config-list[2].percentage = 100
rollout.config-list[2].whitelisting-required = 1
rollout.config-list[2].user-list = 123
rollout.config-list[2].whitelisting-for = ext/v1/spend-analytics/dataBreakup

rollout.config-list[3].percentage = 100
rollout.config-list[3].whitelisting-required = 1
rollout.config-list[3].user-list = 123
rollout.config-list[3].whitelisting-for = ext/v1/spend-analytics/aggregatedData

rollout.config-list[4].percentage = 100
rollout.config-list[4].whitelisting-required = 1
rollout.config-list[4].user-list = 123
rollout.config-list[4].whitelisting-for = ext/v1/cstBotDetail


s3.bucket.name = flink-checkpoints-middleware
s3.bucket.region = ap-south-1
s3.bucket.access.key.value = ********************
s3.bucket.secret.key.value = JQCiH/u12Uollv3ykEZWfJTJzbw+8m6UXxx+YeWg

all.types.of.meta.data = filter,search,spendAnalytics,autoComplete,passbookMenu
metaDataApi.whitelisted.users = -1
#Cache time in ms
cache.ttl.for.metadata.at.app.side = 5000
supported.filters.list.for.all.user = txnCategory
auth-validation-required-for-meta-data = false
meta.data.cache.time = 300

configurable_properties-index-alias = configurable_properties_alias
configurable_properties.source = ElasticSearch
configurable_properties-index = configurable_properties-1

scheduler.schedulerPropertyMap.configurablePropertiesServiceScheduler.name = configurablePropertiesServiceScheduler
# this cron explanation is to run the scheduler 1 time in 1 minute
scheduler.schedulerPropertyMap.configurablePropertiesServiceScheduler.cronExpression = 0 * * * * ?
scheduler.schedulerPropertyMap.configurablePropertiesServiceScheduler.schedulerType = configurablePropertiesServiceScheduler
scheduler.schedulerPropertyMap.configurablePropertiesServiceScheduler.isSchedulerEnabled = true

scheduler.schedulerPropertyMap.metaInfoScheduler.name = metaInfoScheduler
# this cron explanation is to run the scheduler 1 time in 1 minute
scheduler.schedulerPropertyMap.metaInfoScheduler.cronExpression = 0 * * * * ?
scheduler.schedulerPropertyMap.metaInfoScheduler.schedulerType = metaInfoScheduler
scheduler.schedulerPropertyMap.metaInfoScheduler.isSchedulerEnabled = true

scheduler.schedulerPropertyMap.localizedDataCacheScheduler.name = localizedDataCacheScheduler
scheduler.schedulerPropertyMap.localizedDataCacheScheduler.cronExpression = 0 */5 * ? * *
scheduler.schedulerPropertyMap.localizedDataCacheScheduler.schedulerType = localizedDataCacheScheduler
scheduler.schedulerPropertyMap.localizedDataCacheScheduler.isSchedulerEnabled = true

scheduler.schedulerPropertyMap.rateLimitConfigScheduler.name = rateLimitConfigScheduler
# this cron explanation is to run the scheduler 1 time in 1 minute
scheduler.schedulerPropertyMap.rateLimitConfigScheduler.cronExpression = 0 * * * * ?
scheduler.schedulerPropertyMap.rateLimitConfigScheduler.schedulerType = rateLimitConfigScheduler
scheduler.schedulerPropertyMap.rateLimitConfigScheduler.isSchedulerEnabled = true

# this cron explanation is to run the scheduler 1 time in 1 minute
scheduler.schedulerPropertyMap.kubernetesConfigRefreshScheduler.cronExpression = 0 * * * * ?
scheduler.schedulerPropertyMap.kubernetesConfigRefreshScheduler.schedulerType = kubernetesConfigRefreshScheduler
scheduler.schedulerPropertyMap.kubernetesConfigRefreshScheduler.isSchedulerEnabled = true

aws-es-from-date                      = 2022-08-01 00:00:00.000 +0530

#Bo Panel Properties
configurable_properties.source.bo_panel = BOPanel

localization-enabled = true
container.hostname                                = ${HOSTNAME}
internal_localization_api_call_enabled = false

search-filter-disabled = false
UTH_V1_LISTING_URL = https://api-staging.paytm.com/pth/ext/v3/search
UTH_V1_DETAIL_URL = https://api-staging.paytm.com/pth/ext/v3
UTH_V2_LISTING_URL = https://api-staging.paytm.com/pth/ext/v3/search
UTH_V2_DETAIL_URL = https://api-staging.paytm.com/pth/ext/v3
UTH_BG_APP_SYNC_LISTING_URL = https://api-staging.paytm.com/pth/ext/v3/search

#New Listing API Keys
PTH_LISTING_URL_V1 = https://api-staging.paytm.com/pth/ext/v3/listing
PTH_LISTING_FILTER_URL_V1 = https://api-staging.paytm.com/pth/ext/v3/listing/filter
PTH_LISTING_UPI_PASSBOOK_URL_V1 = https://api-staging.paytm.com/pth/ext/v3/upi/listing
PTH_LISTING_UPI_LITE_PASSBOOK_URL_V1 = https://api-staging.paytm.com/pth/ext/v3/upi_lite/listing
PTH_LISTING_UPI_CC_PASSBOOK_URL_V1 = https://api-staging.paytm.com/pth/ext/v3/upi_cc/listing
PTH_LISTING_FILTER_UPI_PASSBOOK_URL_V1 = https://api-staging.paytm.com/pth/ext/v3/upi/listing/filter
PTH_LISTING_FILTER_UPI_LITE_PASSBOOK_URL_V1 = https://api-staging.paytm.com/pth/ext/v3/upi_lite/listing/filter
PTH_LISTING_FILTER_UPI_CC_PASSBOOK_URL_V1 = https://api-staging.paytm.com/pth/ext/v3/upi_cc/listing/filter
PTH_LISTING_URL_V2 = https://api-staging.paytm.com/pth/ext/v3/listing
PTH_LISTING_FILTER_URL_V2 = https://api-staging.paytm.com/pth/ext/v3/listing/filter
PTH_LISTING_UPI_PASSBOOK_URL_V2 = https://api-staging.paytm.com/pth/ext/v3/upi/listing
PTH_LISTING_UPI_LITE_PASSBOOK_URL_V2 = https://api-staging.paytm.com/pth/ext/v3/upi_lite/listing
PTH_LISTING_UPI_CC_PASSBOOK_URL_V2 = https://api-staging.paytm.com/pth/ext/v3/upi_cc/listing
PTH_LISTING_FILTER_UPI_PASSBOOK_URL_V2 = https://api-staging.paytm.com/pth/ext/v3/upi/listing/filter
PTH_LISTING_FILTER_UPI_LITE_PASSBOOK_URL_V2 = https://api-staging.paytm.com/pth/ext/v3/upi_lite/listing/filter
PTH_LISTING_FILTER_UPI_CC_PASSBOOK_URL_V2 = https://api-staging.paytm.com/pth/ext/v3/upi_cc/listing/filter

uth.cst.txn.history.secret.key = ${PAYMENT_TRANSACTION_HISTORY_CST_SECRET_KEY}

# web v1 properties(would need to revisit commented properties)

#elastic-search-host-1                         = localhost
#elastic-search-port                             = 9200
#elastic-search-scheme                           = http



oauth.service.base.url                             = https://accounts-internal.paytm.com:443

#hardcoding clientId and secret as environment variable for these is not created in unified service
oauth.client.id                                    = upi-pth-staging
oauth.client.secret                                = VWbL9i6sLxDUCpdmi7IekZzuMImcggwA
oauth.client.connection.timeout                    = 2000
oauth.read.timeout                                 = 1500

#bank-oauth
#bank.oauth.service.base.url                        = http://bank-oauth.default
#bank.oauth.service.url.category                    = /bank-oauth/ext
#bank.oauth.client.id                               = ${MIDDLEWARE_PAYMENT_HISTORY_BANKOAUTH_CONFIG_CLIENTID}
#bank.oauth.client.secret                           = ${MIDDLEWARE_PAYMENT_HISTORY_BANKOAUTH_CONFIG_CLIENTSECRET}

#hardcoding clientId and secret as environment variable for these is not created in unified service
#bank.oauth.client.id                               = bank-oauth-transaction-history-service-staging
#bank.oauth.client.secret                           = 3b861bc3-0315-4a85-b895-da3530d70099

oauthClient.connectionProvider.name                = webflux
oauthClient.connectionProvider.maxConnections      = 500
oauthClient.connectionProvider.acquireTimeout      = 10000
oauthClient.connectionProvider.maxIdleTime         = 5

#to provide default value for listing page size
listing-page-size        = 8
#fetching max these no txns from range
max-page-size-of-range   = 20
max-page-size-functionality-enable = true



# initially this value will be picked from BO-Panel if it's not present there then this will be default value
# Whether to use OAuth tokeninfo API or not, if it's value is false then /v2/user will be used
use-tokenInfo-api = false

#elastic-search-index = payment_history_alias
#elastic-search-index-prefix = payment-history-
elastic-search-index-prefix-for-details = payment-history


#app.name                                                 = @project.artifactId@
#app.version                                              = @project.version@
#es-host-list                          = localhost
#es-port                               = 9200

dc-es-host-list                       = *************
dc-es-port                            = 9200

third-es-host-list                    = uth-es-app3-nlb-81e82784bb8b158a.elb.ap-south-1.amazonaws.com
third-es-port                         = 9200

second-es-from-date = 2023-08-01 00:00:00.000 +0530

#aws-es-from-date                      = 2022-08-01 00:00:00.000 +0530
analytics-from-date                   = 2022-07-01 00:00:00.000 +0530

#es-socket-timeout                     = 2000
#es-max-retry-timeout                  = 2000
#es-connect-timeout                    = 1000
#es-connect-request-timeout            = 1000

#server.tomcat.accesslog.enabled                          = true
#server.tomcat.accesslog.pattern                          = [%{yyyy-MM-dd HH:mm:ss.SSS}t] %h \"%r\" %s %b %Dms \"%{Referer}i\" \"%{User-agent}i\" \"%{requestIdLogging}r\"
#server.tomcat.accesslog.directory                        = /log/middleware-transaction-history-service
#server.tomcat.accesslog.prefix                           = ${HOSTNAME}_access
#server.tomcat.accesslog.rotate                           = true
#server.tomcat.accesslog.rename-on-rotate                 = true
#server.tomcat.accesslog.file-date-format                 = .yyyy-MM-dd
#server.tomcat.accesslog.buffered                         = false
#server.tomcat.accesslog.request-attributes-enabled       = true

#static-bank-logo-base-url = https://static.orgk.com/uth/images/bank-logo/
#static-category-logo-base-url = https://static.orgk.com/uth/images/category-logo/
#static-status-logo-base-url = https://static.orgk.com/uth/images/status-logo/
#static-wallet-logo-base-url =  https://static.orgk.com/uth/images/wallet-logo/
#static-paytm-logo-base-url  =  https://static.orgk.com/uth/images/paytm-logo/
#static-merchant-logo-base-url  =  https://static.orgk.com/uth/images/merchant-logo/
#static-upi-merchant-logo-base-url  =  https://static.orgk.com/upi/images/merchant-logo/
#static-paymentMode-logo-base-url = https://static.orgk.com/uth/images/payment-intiation-mode/

#Prometheus Monitoring Constants
#prometheus.explorer                                         = TRANSACTION_HISTORY
#prometheus.hostname                                         = ${CUSTOM_MONITORING_SERVICE_HOST}
#prometheus.port                                             = ${CUSTOM_MONITORING_SERVICE_PORT}

# from date to filter data based on backfill completion format: yyyy-MM-dd HH:mm:ss.SSS Z
# error code 4010 needs to be updated whenever this date changes
from-date-listing-filter = 2020-08-01 00:00:00.000 +0530
date-range-from-date-listing-filter = 2020-08-01 00:00:00.000 +0530
default-from-date-listing-month-duration = 1
#default page size
page-size-auto-complete = 20

shut-auto-complete-service = false

#known - outage, any of our system down, to throw non-retriable error
known-issue-at-backend = false

kafka.bootstrap-servers = *************:9092,*************:9092,*************:9092
#*************:9092,*************:9092,*************:9092
aerospike.cache-name                              = uth
aerospike.cache-expiry-time                       = 7200
aerospike.writePolicyDefault.sleepBetweenRetries  = 50
aerospike.writePolicyDefault.expiration = 300
aerospike.writePolicyDefault.socketTimeout = 500
aerospike.writePolicyDefault.totalTimeout = 900
aerospike.readPolicyDefault.sleepBetweenRetries = 30
aerospike.readPolicyDefault.totalTimeout = 900
aerospike.readPolicyDefault.socketTimeout = 400
aerospike.namespace= banksvc
aerospike.host-name=middlewareaerospike-service
aerospike.port=3000
localise.detail.fields=detailNarration,firstInstrument.narration,secondInstrument.narration,recentTransactionInfo.narration,cstorderItem.label,repeatPayment.name
batch.size=50
localise.listing.fields=narration
prepopulate.data.list=Add Money Failed,Add Money Pending,Add Money to %s,Add Money to Paytm Payments Bank,Added Back to Your,Cashback Received,Cashback Received from %s,For Order At,For reversal of money transfer to,From,From Your,In,In Your,Money Added,Money Debited,Money On Hold,Money Paid,Money Received,Money Refunded,Money Restored,Money Sent,Money Transfer Failed,Money Transfer Pending,Money added to %s,Money added to Paytm Payments Bank,Money on hold for Order at %s,Money on hold released by %s,Paid to %s,Payment Failed,Payment Pending,Payment to %s,Received from %s,Refund Failed,Refund for failed transfer to %s,Refund from %s,Refund pending,Reversal of Money Sent,Sent to %s,To,To Your,Transfer to %s,Using Your,Settlement Received from %s,Gift Voucher Received from %s,Gift Voucher Sent To %s,For reversal of failed transfer to,Compensation Received,As compensation for,Automatic Payment made to %s,For Automatic Payment to,Refund Received,Interest earned for %s,Interest Received,Interest Earned,Cash Withdrawal at %s,Cash Withdrawal,At,Cash Withdrawal at,For,Cash Withdrawal at Paytm Bank Agent,ATM Cash Withdrawal at %s,At ATM,At Store,Refund for failed cash withdrawal,For reversal of cash withdrawal at ATM,Cash Deposited at %s,Cash Deposited,Cash Deposited with Paytm Bank Agent,Cash Deposit at ATM\,%s,At ATM Recycler,Cash Deposit,Salary Received,Flexi-Salary Received,Reimbursement Received,Charges Paid,Charges Paid for purchase of Paytm Payments Bank Debit Card,For Purchase of,For Cash Withdrawal at ATM,Charges Paid for transaction at ATM,For Transaction at ATM,For dispute raised against ATM Cash Withdrawal at,For dispute raised against store payment at,Cashback Received from Paytm Payments Bank,Cashback Received from Paytm,Cheque Return Charges,Aadhaar based direct benefit received,Recovery Deduction,Lien Recovery Reversal,Refund of excess card charges,Issuance Fee charged for Debit card,Added for failed transaction,Money transferred,Money paid to %s via cheque,Money paid via cheque,Money received from cheque payment,LPG subsidy received,Deducted by %s,Reversal of Debit Card fees,Charges Refunded,For Failed Purchase of,Charges Refunded for Debit Card,Annual Charge Paid for Debit Card,Charges Refunded for Cheque Book,Charges Refunded for cash withdrawal at ATM,Charges Refunded for ATM transaction,Charges Paid for payment at %s,For Payment at,Charges Refunded for payment at %s,Charges Paid for cash withdrawal at ATM,Charges Paid for ATM transaction,For dispute raised against,For ATM Transaction at,For Failed ATM Transaction at,For Failed Payment at,For dispute raised against online payment at,Charges Paid for payment to %s,For Payment to,For Failed Payment to,Refund for failed cheque payment,Refund for failed cheque payment to %s,For reversal of Cheque Payment to,Money Deducted,For reversal of money received for,For reversal of money received from,Refund for failed payment to %s,Money reversed for failed transfer from %s,Refund for failed transfer via UPI,For reversal of,Fixed Deposit Created,Refund for failed Fixed Deposit creation,Fixed Deposit Redeemed,For reversal of failed,Refund of AePS fund transfer,Refund for AePS Cash withdrawal,AePS fund transfer,AePS Cash withdrawal,Paid Successfully,Recent Payments With %s,Paid,Received,Refunded,Recent Gift Vouchers,Sent,Added to %s,Pending Add Money to %s,Failed Add Money to %s,Created,Deposited at %s,Withdrawal at Paytm Bank Agent,Withdrawal at %s,Earned for %s,Need Help with this Payment,View more details,Foreign Remittance Received from,Paid for Outward Remittance to,Refund of Outward Remittance sent to,RECEIVED IN
retry.topic.name = pth_retry_data
bankData.blackListed.reportCodes = 1
reportcodes.blocked.for.ShowInListing = 1
bankPassbook.Rptcodes.whilelisted.userslist = 1
bankPassbook.Rptcodes.whilelisteding.percent= 100
bankData.blackListed.dccId = 1
bankData.blackListed.key.deviceId.value.acquirerId = {1:1}
bankData.whiteListed.SchemeCodes = SAINF,SAIND,SALRF,SALRY,WMNSA,WMNSF,SRCIT,SRCIF,BSBDF,BSBDA,MIIND,MIINF
show-bank-data = false
rpt.paymnt.to.mobile.url                  = paytmmp://cash_wallet?featuretype=sendmoneymobile&recipient=%{recipient}
rpt.paymnt.url.p2p.vpa2vpa                = paytmmp://cash_wallet?featuretype=money_transfer&pn=%{pn}&pa=%{pa}
rpt.paymnt.url.p2p.ppblaccount2vpa.inward=paytmmp://cash_wallet?featuretype=money_transfer&pn=%{remitterName}&pa=%{remitterAcctNum}
rpt.paymnt.url.p2m.android                = https://qr.paytm.in/%{qrId}
rpt.paymnt.url.p2m.ios                    = paytmmp://cash_wallet?recipient=%{qrId}
rpt.paymnt.url.p2m.paytQR.mid             = paytmmp://cashier_page?mid=%{mId}
rpt.paymnt.url.p2m.paytmQR.vpa            = paytmmp://pay?pn=%{merchantName}&pa=%{vpa}
rpt.paymnt.url.imps.neft.xfer             = paytmmp://cash_wallet?featuretype=money_transfer&pn=%{benefName}&account=%{benefAcctNum}&ifsc=%{benefIfsc}&bank_name=%{beneficiaryBank}
rpt.paymnt.url.imps.neft.xfer.inward      = paytmmp://cash_wallet?featuretype=money_transfer&pn=%{remitterName}&account=%{remitterAcctNum}&ifsc=%{remitterIfsc}&bank_name=%{remitterBank}
rpt.paymnt.url.p2m.upi                    = paytmmp://cash_wallet?featuretype=money_transfer&pn=%{merchantName}&pa=%{vpa}
rpt.payment.url.for.selfTransfer=paytmmp://cash_wallet?featuretype=money_transfer_self&popToRoot=false
rpt.paymnt.url.p2p.vpa2account=paytmmp://pay?paytmAr=%{accRefNum}&pn=%{pn}
rptPayment.enabled.for.selfTransfer=true
rptPayment.enabled.for.vpa2account=false
blacklisted.handles.for.vpa.rpt.pmt = paytm
mobileNo.deeplink.enabled.for.vpa2vpa = true
repeatPayment.vpaBasedForPaytmQrMerchantsEnable = true
repeatPayment.blockingPaytmMerchantVpaPrefixForVpaBasedRepeatPayment = paytm-

#deepLink for UPI Lite Cta
deepLink.upi.lite.cta                     = paytmmp://mini-app?aId=7ee85e9b76c544f59ccc0c8b5fcdd655&data=eyJzcGFyYW1zIjp7ImNhblB1bGxEb3duIjpmYWxzZSwic2hvd1RpdGxlQmFyIjpmYWxzZSwicGF5dG1DaGFuZ2VTdGF0dXNCYXJDb2xvciI6IiNjZWVjZmYiLCJzdGF0dXNCYXJTdHlsZSI6MX0sInBhdGgiOiIvIy9tdC91cGktbGl0ZS9iYW5rLXNlbGVjdGlvbiJ9
whitelisted-bank-users = *********,*********
whitelisted-localisation-users =-1
salary.reportCodes = 90100,90110,90120
clientConfig.cron.expression = 0 0 1 * * ?
scheduler.cron.expression = 0 ${random.int[0,59]} 0 * * ?

#configurable_properties-index-alias = configurable_properties_alias
configurable_properties.scheduler.cron.expression = * * * * * ?
#configurable_properties.source = ElasticSearch
#configurable_properties-index = configurable_properties-1
configurable_properties.source.elastic_search = ElasticSearch
configurable_properties.source.dynamodb = dynamoDB
configurable_properties.source.table = configurable_properties

#Bo Panel Properties
#configurable_properties.source.bo_panel = BOPanel

jwt.verification.enable=true
#container.hostname                                = ${HOSTNAME}

mapOf.ElasticSearchTemplate.templateFile          = {\
                                                      'configurable_properties_template': 'esConfigurationPropertiesTemplate.json' \
                                                   }
updateElasticSearchTemplate                      = true
updateElasticSearchV2Template                    = false


upi.whitelisted.users = -1

#Merchant logo Base url

#metaDataApi.whitelisted.users = -1
#envBased.jacocoSupport=false
fetch-user-image-from-cache = true
push-missing-user-image-list-to-kafka = true

#Kafka properties
kafka.target-v2-list[9].kafka-client-name               = recon_config
kafka.target-v2-list[9].kafka-producer-key              = RECON_CONFIG_DATA_PRODUCER
kafka.target-v2-list[9].topic                           = middleware_transaction_history_recon_config_data
kafka.target-v2-list[9].bootstrap-servers               = localhost:9092
kafka.target-v2-list[9].batch-size-bytes                = 3000
kafka.target-v2-list[9].confluent-kafka-registry-url    = http://localhost:8081
kafka.target-v2-list[9].linger-ms                       = 15
kafka.target-v2-list[9].request-timeout-ms              = 5000

kafka.target-v2-list[10].kafka-client-name               = txnHistoryUserImageClient
kafka.target-v2-list[10].kafka-producer-key              = user_image_txn_history_producer
kafka.target-v2-list[10].topic                           = user_image_url_data
kafka.target-v2-list[10].bootstrap-servers               = localhost:9092
kafka.target-v2-list[10].batch-size-bytes                = 3000
kafka.target-v2-list[10].linger-ms                       = 15
kafka.target-v2-list[10].request-timeout-ms              = 5000

kafka.target-v2-list[12].kafka-client-name               = api_response_cache_population
kafka.target-v2-list[12].kafka-producer-key              = api_response_cache_population_CLIENT_PRODUCER
kafka.target-v2-list[12].topic                           = middleware_transaction_history_back_fill_data
kafka.target-v2-list[12].bootstrap-servers               = localhost:9092
kafka.target-v2-list[12].batch-size-bytes                = 3000
kafka.target-v2-list[12].linger-ms                       = 15
kafka.target-v2-list[12].request-timeout-ms              = 5000

kafka.target-v2-list[13].kafka-client-name               = toggle-visibility
kafka.target-v2-list[13].kafka-producer-key              = toggle-visibility_CLIENT_PRODUCER
kafka.target-v2-list[13].topic                           = pth_toggle_visibility_data
kafka.target-v2-list[13].bootstrap-servers               = localhost:9092
kafka.target-v2-list[13].batch-size-bytes                = 3000
kafka.target-v2-list[13].linger-ms                       = 15
kafka.target-v2-list[13].request-timeout-ms              = 5000

#base.url.for.merchant.logo = https://catalog-staging.paytm.com/images/
#url.for.ondc.logo = https://assetscdn1.paytm.com/images/catalog/view_item/1519347/*************.png
#ondc.verticalId = 204
#cart.whitelistedMerchantVerticalIds = 204
#cart.whitelisted.userIds.list.for.narration = -1
upi.timeline.baseUrl = https://upisecurev4-staging.orgk.com
upi.timeline.uri =  /upi/int/txn/v4/transaction/status
upi.timeline.sync.connection.timeout = 500
upi.only.timeline.connection.timeout = 500
upi.timeline.socket.timeout = 500
upi.timeline.timeout = 500
upi.timeline.secret.key = 83738hbfjfbfjb2565265lkldkldk

timeline.url.path = /payments-history-v2/ext/v2/{sourceContext}/detail?txnId={txnId}&status={status}&showOnlyTimeline=true
timeline.server = https://api.orgk.com

recon.config.cron1.delay.in.milli = 50000
recon.config.cron1.lock.at.most.in.milli = 500000

#Value is request Param in filterNode

outwardInternationalRemittance.url = paytmmp://payment_bank?pageId=passbookPage&featuretype=outward_remittance
#supported.filters.list.for.all.user = txnCategory
#Cache time in ms
#cache.ttl.for.metadata.at.app.side = 5000
#All types of metaData
#all.types.of.meta.data = filter,search,spendAnalytics,autoComplete
chat.url.path = paytmmp://chat?featuretype=start_chat
min.Length.Of.AutoComplete.Query = 3
min.Length.Of.Search.Query = 3
max.Time.To.Print.AutoComplete.Logger = 100


#Value is request Param in filterNode

detail-retriable-count = 1
detail-retriable-wait-time = 0

#managed-es-host = https://vpc-ite-v1-mid-paymenthistoryaes-h7avcdtvy5imsoulw3734z4fjy.ap-south-1.es.amazonaws.com
managed-es-v2-host = localhost
#whitelisted.users.list.for.managed.es = 123
managed.roll.out.percentage = 0

enable.unmask.accNumber = false

primary.es.cluster = V0
max-length-of-autoComplete-query = 20

max-Num-Of-ContactBook-Numbers = 4
contactbook-search-whitelisted-users-custId-Identifier = -1
contactbook-search-whitelisting-percentage = 100

#oauth.token.evaluation.skip = false

rollout.config-list[5].percentage = 100
rollout.config-list[5].whitelisting-required = 0
rollout.config-list[5].whitelisting-for = detailPageRoleOutFeature

rollout.config-list[6].percentage = 100
rollout.config-list[6].whitelisting-required = -1
rollout.config-list[6].whitelisting-for = listingCache

rollout.config-list[7].percentage = 100
rollout.config-list[7].whitelisting-required = -1
rollout.config-list[7].whitelisting-for = upiPassbookCache

rollout.config-list[8].percentage = 100
rollout.config-list[8].whitelisting-required = 1
rollout.config-list[8].user-list = 0
rollout.config-list[8].whitelisting-for = taggingOnDetails

rollout.config-list[9].percentage = 0
rollout.config-list[9].whitelisting-required = 1
rollout.config-list[9].user-list = *********,*********,*********,*********,*********,*********,7881770,*********,********,**********,*********,**********,**********,**********,********,**********
rollout.config-list[9].whitelisting-for = DcListingRouting

rollout.config-list[10].percentage = 0
rollout.config-list[10].whitelisting-required = 0
rollout.config-list[10].whitelisting-for = DcNonTransactingHandling

rollout.config-list[11].percentage = 100
rollout.config-list[11].whitelisting-required = 1
rollout.config-list[11].user-list = *********,*********
rollout.config-list[11].whitelisting-for = UpiCcEmiCta

rollout.config-list[12].percentage = 0
rollout.config-list[12].whitelisting-required = 1
rollout.config-list[12].user-list = *********,*********,*********,*********,*********,*********,7881770,*********,********,**********,*********,**********,**********,**********,********,**********,6236193
rollout.config-list[12].whitelisting-for = UthAnalyticsWhiteListing

rollout.config-list[13].percentage = 0
rollout.config-list[13].whitelisting-required = 1
rollout.config-list[13].user-list = **********
rollout.config-list[13].whitelisting-for = UserRateLimitingWhiteListing

rollout.config-list[14].percentage = 0
rollout.config-list[14].whitelisting-required = 1
rollout.config-list[14].user-list = *********,*********,*********,********,*********,*********,**********,**********,**********,**********,*********,*********,*********,*********,**********,********,********,*********
rollout.config-list[14].whitelisting-for = TomcatListingRoleOutFeature

rollout.config-list[15].percentage = 0
rollout.config-list[15].whitelisting-required = 1
rollout.config-list[15].user-list = *********,*********,*********,********,*********,*********,**********,**********,**********,**********,*********,*********,*********,*********,**********,********,********,*********
rollout.config-list[15].whitelisting-for = bankOauthApiRollout

rollout.config-list[16].percentage = 0
rollout.config-list[16].whitelisting-required = 1
rollout.config-list[16].user-list = *********,*********,*********,********,*********,*********,**********,**********,**********,**********,*********,*********,*********,*********,**********,********,********,*********
rollout.config-list[16].whitelisting-for = recentTxns

rollout.config-list[17].percentage = 0
rollout.config-list[17].whitelisting-required = 1
rollout.config-list[17].user-list = *********,*********,*********,********,*********,*********,**********,**********,**********,**********,*********,*********,*********,*********,**********,********,********,*********
rollout.config-list[17].whitelisting-for = autoTaggingFeature

detailPageApiCachingEnabled = false
isListingCacheEnabled = false
isUpiPassbookCacheEnabled = false

need-to-convert-received-Query = true
autocomplete-whitelisted-custIds = -1
autocomp-new-query-whitelisting-percentage = 100

searchFields-userView-mapping = {\
                                    "searchFields.searchOtherName":"Name", \
                                    "searchFields.searchOtherBankName":"Bank Name", \
                                    "searchFields.searchOtherMobileNo":"Mobile No.", \
                                    "searchFields.searchPaymentSystem":"Account", \
                                    "searchFields.searchTxnIndication":"Type", \
                                    "searchFields.searchTxnCategory":"Type", \
                                    "searchFields.searchTxnStatus":"Status", \
                                    "searchFields.searchUthMerchantCategory":"Category", \
                                    "searchFields.searchOtherVpa":"UPI ID", \
                                    "searchFields.searchVoucherName":"Voucher", \
                                    "searchFields.searchWalletType":"Sub Wallet", \
                                    "searchFields.searchOtherAccountNo":"Account Number", \
                                    "searchFields.searchSelfBankName":"Bank Name", \
                                    "searchFields.searchTags":"Tag", \
                                    "searchFields.searchSelfAccountNo":"Account Number", \
                                    "searchFields.searchSelfAccTyp":"Account" }

#metadata api Oauth Call
oauth.call.enable.in.metadata.api = true

#this cron runs every 0th,10th,20th,20th second of 0 to 5th minute at 12:00 am on 1st of every month
configurable.scheduler.create.new.indices.cron.expression = 0,10,20,30 0-5 0 1 * ?
#updated.index.month.list = payment-history-07-2020,payment-history-08-2020,payment-history-09-2020,payment-history-10-2020,payment-history-11-2020

#ForEnabling Req Money Cta
enable.requestMoney.cta = true

#For routing to Dc cluster.
isListingRoutingEnabled = true
isDetailRoutingEnabled = true

#Maximum Days Diff for tagging a txn.
maxAllowedDayDiffForTagging = 30

txn-date-mandatory-for-detail-api = false



ipoMandate.h5.base.url = paytmmp://mini-app?aId=bf0fde1e742c426bbaacadab0e33b09e&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiLyMvbXQvYXV0b21hdGljLXBheW1lbnRzL2ZhcS9pcG8ifQ==
onHold.h5.base.url = paytmmp://cash_wallet?featuretype=cash_ledger&tab=subwallet&cta_type=onhold
manage.automatic.payment.cta.base.deeplink =  paytmmp://mini-app?aId=bf0fde1e742c426bbaacadab0e33b09e&data=eyJzcGFyYW1zIjp7ImNhblB1bGxEb3duIjpmYWxzZSwic2hvd1RpdGxlQmFyIjpmYWxzZX0sInBhdGgiOiIvIy9tdC9hdXRvbWF0aWMtcGF5bWVudHMvbWFuZGF0ZS1kZXRhaWxzIn0=&umn=
recurringMandate.h5.base.url = paytmmp://mini-app?aId=bf0fde1e742c426bbaacadab0e33b09e&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiLyMvbXQvYXV0b21hdGljLXBheW1lbnRzL2ZhcS9tYW5kYXRlcyJ9

deepLinks.wmcDescription = paytmmp://sflanding?url=https://storefront.paytm.com/v2/h/know-more-wallet-maintenance-charges&backbtn=true
deepLinks.walletReactivate = paytmmp://cash_wallet?featuretype=add_money&tab=wallet

#search-filter-disabled = false

#<br><br> is added in below message to provide 2 line breaks on App
deemed-txn-msg = This payment is pending because the receiver's bank is facing issues in accepting the payment.<br><br>Don't worry, your money is safe. We are continuously checking the status of your payment and request you to not make multiple payments. Banks may take upto 3-5 working days to confirm the final status of your payment. In case this payment fails, your bank will automatically refund money to your account.

# APP Side Cache variable :
appSideCache.invalidateVersion = 12
appSideCache.latestInvalidateVersion = 13
appSideCache.invalidateVersion.enabled = false
# 15*24*60*60*1000 = ********** 15 days
updatesApi.maxTimeGapForUpdatesApi = **********
# 30*24*60*60*1000 = ********** 1 month
# max time the fromDate can go back for serving updates
updatesApi.maxFromDateGapForUpdatesApi.days = 100
updatesApi.fromDateGap = **********
updatesApi.pageSize = 800
# set updateDate on listing response only when diff of(currentDate - updateDate) <= D days, currently(D = 7)
searchApi.setUpdatedDate.onlyWhenDiffIsWithinXDays = 7
# appCacheRecon variable
appCacheRecon.enabled = true
appCacheRecon.fromDateGap = **********
appCacheRecon.invalidateStoredDataFlag.enable = false

tagFeatureEnable = true

#Url to get next listing hit
listingUrl = UTH_V1_LISTING_URL
bgAppSyncListingUrl = UTH_BG_APP_SYNC_LISTING_URL

#UTH_V1_LISTING_URL = https://api.orgk.com/transaction-history/ext/v3/search
#UTH_V1_DETAIL_URL = https://api.orgk.com/transaction-history/ext/v3
#UTH_V2_LISTING_URL = https://api.orgk.com/transaction-history-v2/ext/v3/search
#UTH_V2_DETAIL_URL = https://api.orgk.com/transaction-history-v2/ext/v3

#localization-enabled = true
listing-localization-enabled = true

other-party-bank-details-enabled = false
other-party-bank-details-for-vpa2account-txn-enabled = true
other-party-bank-details-enabled-inward = false
min-amt-req-to-show-other-party-info-in-outward-txn-in-paisa = 1000
env.based.prefix =

oauth.invalid.token.exception.response.enable = true

#Route nonTransactingUsers to DC or not.
nonTransactingRoutingToDc_upiLite = true
nonTransactingRoutingToDc_allTxns = true

#Whether to use customerCreationDate or not
isUseOfCustomerCreationDateEnabled = false

repeat-payment-filter-enabled = true
showTagEnable = true
tagsUpdationEnable = true

cta.repeatPayment.use-mobileNo-deeplink-for-upi-p2p-txns = false

repeatPayment.legacyImplementationEnabled = false

#deeplink for Storecash Cta
storecash-cta-enabled = false
deepLink.storecash.cta                    = paytmmp://mini-app?aId=74c710393f38477eb7994901e7e80307
uth.analytics.send.to.kafka = true

poweredbyurl.enable.in.footerlogo = true


# Format for cta property name :- cta.type_of_cta.functionality_description
uth.user.rate.limit.per.hour = 360
uth.user.rate.limit.hash.algo = SHA256
uth.user.rate.limit.enable = true

deepLink.convert.emi.cta = paytmmp://mini-app?aId=11260227834849508d8d135d2ca00ba2&data=eyJzcGFyYW1zIjp7ImNhblB1bGxEb3duIjpmYWxzZSwic2hvd1RpdGxlQmFyIjpmYWxzZSwic3RhdHVzQmFyU3R5bGUiOjEsInBheXRtQ2hhbmdlU3RhdHVzQmFyQ29sb3IiOiIjMTAxMDEwIn0sInBhdGgiOiIvaXRlMS9pbmRleC5odG1sI210L2NjLWVtaS9hY3RpdmF0ZSJ9
deepLink.view.emi.cta = paytmmp://mini-app?aId=11260227834849508d8d135d2ca00ba2&data=eyJzcGFyYW1zIjp7ImNhblB1bGxEb3duIjpmYWxzZSwic2hvd1RpdGxlQmFyIjpmYWxzZSwic3RhdHVzQmFyU3R5bGUiOjEsInBheXRtQ2hhbmdlU3RhdHVzQmFyQ29sb3IiOiIjMTAxMDEwIn0sInBhdGgiOiIvaXRlMS9pbmRleC5odG1sI210L2NjLWVtaS9icmVha3VwIn0=
upi.cc.emi.credit.card.cycle.days = 45
upi.cc.emi.cta.enable = false

status-wise-custom-message-enabled-in-detail-page = true
white-listed-apis-for-traffic-diversion.on.rate.limit = pth.ext.v3.search
white-listed-apis-for-traffic-diversion = pth.ext.v3.search

blacklisted.apis.for.rate.limit = payments-history-v2.ext.v1.ping,payments-history-v2.ext.v1.health,pth.ext.v1.ping,pth.ext.v1.health

blacklisted.apis.for.filters = payments-history-v2.ext.v1.ping,payments-history-v2.ext.v1.health,pth.ext.v1.ping,pth.ext.v1.health

#While adding prodmtdc profile for tomcat need to keep this property false there.
isTrafficDiversionEnabled = false

uth.user.daily.rate.limit.enable = false
uth.user.daily.rate.limit.threshold = 10

uth.user.rate.limit.apis.blacklisted = /payments-history-v2/ext/v1/metaData

upi-cstPanel-api-enabled = true

#key is verticalId and value will contain List<GenericCtaInfo>
txn.details.config.mapping.for.onus.merchant.verticalId = {\
  "94": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId94","deeplink":"paytmmp://mini-app?aId=08f1adddf843430c9cbe7729a3d000a2&data=$parametersBase64Hash","parameters":{"sparams":{"showTitleBar":false,"order_id":"$orderId"},"path":"/h5-recharge-pos/v1/index.html"}}], \
  "174": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId174","deeplink":"paytmmp://mini-app?aId=edc6391a24ac4eec8b2d3558d5b53195&data=$parametersBase64Hash","parameters":{"sparams":{"showTitleBar":false},"params":"?utm_source=passbook","path":"/myorders/$orderId"}},  \
          {"ctaType":"CHAT_PROFILE","ctaLabelLocaleKey": null,"deeplink":"paytmmp://mini-app?aId=edc6391a24ac4eec8b2d3558d5b53195&data=$parametersBase64Hash","parameters":{"sparams":{"pullRefresh":false,"canPullDown":false,"showTitleBar":false},"path":"/store/$shopId?utm_source=uth_redirect"}}], \
  "66": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId66","deeplink":"paytmmp://mini-app?aId=358b2bd4936a4f34918c620a3c7ac4f9&data=$parametersBase64Hash","parameters":{"sparams":{"showTitleBar":false},"params":"?utm_source=passbook","path":"/myorders/$orderId"}}],\
  "87": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId87","deeplink":"paytmmp://mini-app?aId=77a7aa36c00e469482a6219004fde717&data=$parametersBase64Hash","parameters":{"params":"","path":"/city-bus/orderSummary/$orderId","sparams":{"pullRefresh":false,"canPullDown":false,"showTitleBar":false}}}], \
  "72": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId72","deeplink":"paytmmp://train_order_summary_v2?url=https://cart.paytm.com/v1/myOrders/$orderId&from=h5"}], \
  "26": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId26","deeplink":"paytmmp://bus_order_summary?url=https://cart.paytm.com/v1/myOrders/$orderId?&isfromorderhistory=0&vertical=bus"}],\
  "64": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId64","deeplink":"paytmmp://flight_order_summary?url=https://cart.paytm.com/v1/myOrders/$orderId&isfromorderhistory=0"}],\
  "70": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId70","deeplink":"paytmmp://movie_order_summary?order_id=$orderId"}],\
  "73": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId73","deeplink":"paytmmp://events?insiderH5Url=https://h5.insider.in/payments/status/fromOrders/orderId/$orderId"}],\
  "204": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId204","deeplink":"paytmmp://product?url=https://paytmmall.com/shop/summary/$orderId#html"}],\
  "84": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId84","deeplink":"paytmmp://mini-app?aId=08f1adddf843430c9cbe7729a3d000a2&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiL2g1LXJlY2hhcmdlLXBvcy92MS9pbmRleC5odG1sIn0=&utm_soruce=app&utm_medium=passbook-view-detail&order_id=$orderId"}],\
  "76": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId76","deeplink":"paytmmp://mini-app?aId=08f1adddf843430c9cbe7729a3d000a2&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiL2g1LXJlY2hhcmdlLXBvcy92MS9pbmRleC5odG1sIn0=&utm_soruce=app&utm_medium=passbook-view-detail&order_id=$orderId"}],\
  "83": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId83","deeplink":"paytmmp://gold?url=https://cart.paytm.com/v1/myOrders/$orderId&order-summary-type=Gold&From=Order_history&order_id=$orderId"}],\
  "105": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId105","deeplink":"paytmmp://mini-app?aId=08f1adddf843430c9cbe7729a3d000a2&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiL2g1LXJlY2hhcmdlLXBvcy92MS9pbmRleC5odG1sIn0=&utm_soruce=app&utm_medium=passbook-view-detail&order_id=$orderId"}],\
  "17": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId17","deeplink":"paytmmp://mini-app?aId=08f1adddf843430c9cbe7729a3d000a2&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiL2g1LXJlY2hhcmdlLXBvcy92MS9pbmRleC5odG1sIn0=&utm_soruce=app&utm_medium=passbook-view-detail&order_id=$orderId"}],\
  "90": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId90","deeplink":"paytmmp://mini-app?aId=08f1adddf843430c9cbe7729a3d000a2&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiL2g1LXJlY2hhcmdlLXBvcy92MS9pbmRleC5odG1sIn0=&utm_soruce=app&utm_medium=passbook-view-detail&order_id=$orderId"}],\
  "4": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId4","deeplink":"paytmmp://mini-app?aId=08f1adddf843430c9cbe7729a3d000a2&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiL2g1LXJlY2hhcmdlLXBvcy92MS9pbmRleC5odG1sIn0=&utm_soruce=app&utm_medium=passbook-view-detail&order_id=$orderId"}],\
  "56": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId56","deeplink":"paytmmp://mini-app?aId=08f1adddf843430c9cbe7729a3d000a2&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiL2g1LXJlY2hhcmdlLXBvcy92MS9pbmRleC5odG1sIn0=&utm_soruce=app&utm_medium=passbook-view-detail&order_id=$orderId"}],\
  "187": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId187","deeplink":"paytmmp://mini-app?aId=08f1adddf843430c9cbe7729a3d000a2&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiL2g1LXJlY2hhcmdlLXBvcy92MS9pbmRleC5odG1sIn0=&utm_soruce=app&utm_medium=passbook-view-detail&order_id=$orderId"}],\
  "71": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId71","deeplink":"paytmmp://order_summary?url=https://cart.paytm.com/v2/myorders/$orderId/detail"}]\
  }

view-details-cta-enabled = true
show-benef-acc-number = true

chat.profile.cta.for.onus.enabled = true
chat.profile.cta.for.non.onus.enabled = true

postpaid.vertical.id = 94

pushToKafkaForAuditEnabled = true
minTimeDiffForDualWriteAuditMs = 4000
dualWriteAuditStartDate = 2023-10-28 00:00:00.000 +0530

uth.downstream.auth.circuit.breaker.config = {\
  "refreshPeriod":"60",\
  "limit":"1000"}

uth.downstream.auth.circuit.breaker.enable = false
uth.downstream.auth.circuit.breaker.failure.httpcodes = 500,502,503,504
uth.downstream.auth.circuit.breaker.failure.classes = RequestTimeOutException

txn.stream.lag.based.tracking.feature.enabled           = false
txn.stream.lag.based.txn.details.rejections.enabled     = false
txn.details.source.txn.stream.max.lag.rejection.buffer.seconds   = 1

amount-range-filter-enabled = true

pth-pth.consumer.service-secret = PTH_CONSUMER_SERVICE_SECRET
statement-service-es-fetch-page-size = 10
statement-service-disabled = false

cta.enable-view-history-for-onus-txns = false

rewind.meta.data.cache.enabled = false
rewind.txn.analytics.data.cache.enabled = false

min.amt.limit.in.paisa.to.show.convert.emi.cta = 200000

bootWithoutConfigs.boPanelConfigs = true

recap.meta.data.index = recap-meta-data-index

detail.view.from.parent.txn.enabled = true

#Will change to environment variable for prod after injecting key in prod.
uth.ts.cst.secret.key = f2srt64r57mT45cdws

merchant.type.change.based.on.oms.order.logic.enable = true

rewind.api.auth.skipping.enabled = true

recap.es.indexing.enabled = true

recap.cache.write.enabled = true

online.deals.verticalId  = 66
gv.verticalId            = 66
offline.deals.verticalId = 174

#need to ask
online.deals.logoUrl     = https://assetscdn1.paytm.com/images/catalog/view_item/2283581/1699599295957.png
gv.logoUrl               = https://consumergv.paytm.com/gv-custom-files/images/gift-cards.png
offline.deals.logoUrl    = https://assetscdn1.paytm.com/images/catalog/view_item/1799274/1699276411188.png

RecapInvalidDataCheckSkip = **********,*********

fuzzy.search.required.for.autoComplete.api = false

need.help.cta.config.map = {\
  "ocl" : {"ctaType":"CST_NEED_HELP","ctaLabelLocaleKey":"ctaLabelLocaleKeyForOclCstDeeplink","deeplink":"paytmmp://csttree?featuretype=cst_issue&isOnusTxn=$isOnusTxn&PI=$PI&urlType=launchBot&calledFrom=uth_detail_page&transactionId=$txnId&showHomeOnBack=false"},\
  "upi" : {"ctaType":"CST_NEED_HELP","ctaLabelLocaleKey":"ctaLabelLocaleKeyForUpiCstDeeplink","deeplink":"paytmmp://csttree?featuretype=cst_issue&isOnusTxn=$isOnusTxn&PI=$PI&urlType=launchBot&calledFrom=uth_detail_page&transactionId=$txnId&transactionDate=$txnDate&showHomeOnBack=false&isTpap=true"},\
  "wallet" : {"ctaType":"CST_NEED_HELP","ctaLabelLocaleKey":"ctaLabelLocaleKeyForWalletCstDeeplink","deeplink":"paytmmp://mini-app?aId=5bdde7c737ee423b98beb769b281df5e&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXJhbXMiOiI/ZmVhdHVyZXR5cGU9Y29tbW9uX21pbmkmb3Blbl9zY3JlZW49Y3N0X2Zsb3cifQ==&itemStatus=$itemStatus&itemName=$itemName&narration=$narration&walletTxnId=$txnId&walletTxnTypeCode=$txnType&launchOrderBot=true"},\
  "bank" : {"ctaType":"CST_NEED_HELP","ctaLabelLocaleKey":"ctaLabelLocaleKeyForBankCstDeeplink","deeplink":"paytmmp://mini-app?aId=5bdde7c737ee423b98beb769b281df5e&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXJhbXMiOiI/ZmVhdHVyZXR5cGU9Y29tbW9uX21pbmkmb3Blbl9zY3JlZW49Y3N0X2Zsb3cifQ==&cbsTxnId=$txnId&cbsTxnSerialNumber=$txnSerialNum&cbsReportCode=$reportCode&cbsTxnDate=$txnPostDate&cbsAccountId=$accountId&cbsTxnType=$txnType&itemName=$itemName&rrnNumber=$rrn&launchOrderBot=true"},\
  "toll" : {"ctaType":"CST_NEED_HELP","ctaLabelLocaleKey":"ctaLabelLocaleKeyForTollCstDeeplink","deeplink":"paytmmp://mini-app?aId=5bdde7c737ee423b98beb769b281df5e&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXJhbXMiOiI/ZmVhdHVyZXR5cGU9Y29tbW9uX21pbmkmb3Blbl9zY3JlZW49Y3N0X2Zsb3cifQ==&tollTxnId=$tollTxnId&itemName=$itemName&itemStatus=$itemStatus&launchOrderBot=true"},\
  "default" : {"ctaType":"CST_NEED_HELP","ctaLabelLocaleKey":"ctaLabelLocaleKeyForDefaultCstDeeplink","deeplink":"paytmmp://contactus"}\
  }

need-help-cta-enabled = true

recentTxns.disabled = false
recentTxns.list.of.supportedStatus = 1,2,3
recentTxns.cache.ttl.seconds = 300
ptl.apiVersion.start = 2.0

deepLink.deaf.txn.faq.cta = http://orgk.com/customer-service/DEAFund
enable-deaf-txn-faq-cta = false

#For fetching the dc from date on AWS servers
prodmtdc-from-date-listing-filter : 2022-11-01 00:00:00.000 +0530

rptPayment.disabled.bankAndWallet=true

detail.blacklisted.stream.source = 1,4,5,6

# Powered By Logo mapping wrt upiHandle
poweredBy.upiLogo.list = paytm


cst.pth.txnList.api.secret = cA9AJxZSC7tLASkY
fullScanForSecondaryDbEnabled=true


# List of CTA Type Enabled
detailPage.listOfCtaType.enabled = CST_NEED_HELP,VIEW_UPI_LITE,SPLIT_BILL,DESCRIPTION,MANAGE_AUTOMATIC_PAYMENT

#secret for TPAP PANEL client for cstPanel api
cst.pth.cstPanel.api.secret = ${PTH_TPAP_PANEL_SECRET_KEY}

mandate.history.cst.secret = *********
mandate-history-from-date = 2017-01-01 00:00:00.000 +0530
mandate-history-parent-es-alias = mandate_info_alias
mandate-history-child-es-alias = mandate_activity_alias

#mandate history api
mandate.history.baseUrl = https://upi.paytm.com/bff
mandate.history.uri =  /upi/ext/gateway/v1/mandate/journey
mandate.history.connection.timeout = 500
mandate.history.socket.timeout = 500
mandate.history.timeout = 500
#need to create this secret key
mandate.history.api.secret.key = asdfghjkl

isSearchableStringEnable = false
setMccCodeInResponse = false

mandate-journey-api-es-fetch-limit = 100

upi-lite-filter-extra-handling-enabled = false

paytm.tpap.handles.list = paytm,pthdfc,ptyes,ptaxis,ptsbi

# This is the cutoff date to identify new customer currentvalue :- 24 October 2024.
cutoffDateToIdentifyNewCustomerInEpoch = 1729708200000

configurable_properties.source.bo_panel.pageSize=100
upi-cc-filter-from-date = 2024-10-01 00:00:00.000 +0530

onus-vertical-filter-from-date = 2024-11-01 00:00:00.000 +0530

vertical.name.to.ids.mapping={\
  "Recharges & Bill Payments":[4,17,56,71,76,84,86,87,90,105,155],\
  "Gift Cards & Deals":[5,66,85,174],\
  "Travel":[26,64,60,72,167,126,181],\
  "Movies & Events":[70,40,55,73,74],\
  "Shopping":[2,6,7,8,9,10,11,12,13,14,15,18,19,20,22,23,24,25,30,31,33,34,35,36,37,38,41,42,43,44,45,46,47,49,50,51,53,58,63,67,68,69,77,78,80,99,107,117,118,125,127,129,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,156,157,159,162,163,166,169,170,171,178,182,186,204],\
  "Insurance":[79,172,173],\
  "Paytm Gold":[82,83,91],\
  "Paytm Stores":[120,180,190,191,194]\
  }


redirect.request.timeout.in.millis = 2000

disable.repeat.payment.cta.vpa.list.p2p.inward.payout = paytm.payouts@icici,one97735@icici

txn.details.repeat.payment.cta.disabled.txn.status.list = PENDING
txn.details.repeat.payment.cta.disabled.txn.status.failure.error.codes.list =

listing.description.cta.for.recurring.mandate.enabled = false
listing.description.cta.for.ipo.mandate.enabled = false
detail.description.cta.for.recurring.mandate.enabled = false
detail.description.cta.for.ipo.mandate.enabled = false

view_history_cta_enabled_for_vpa_to_account_txn = true
view_history_cta_enabled_for_vpa_to_vpa_txn = true
view_history_cta_enabled_for_vpa_to_3p_vpa_txn = true
view_history_cta_enabled_for_third_party_merchant_txn = true
view_history_cta_enabled_for_ofus_merchant_txn = true
view_history_cta_enabled_for_onus_merchant_txn = true

view_history_cta_deeplink_for_vpa_to_account_txn = paytmmp://chat?featuretype=start_chat&userType=BANK&bankName=$bankName&accRefId=$accRefId&maskedAccNo=$maskedAccountNo&source=passbook_view_history
view_history_cta_deeplink_for_vpa_to_vpa_txn = paytmmp://chat?featuretype=start_chat&userType=CUSTOMER&custId=$custId &custName=$name&source=passbook_view_history
view_history_cta_deeplink_for_vpa_to_3p_vpa_txn = paytmmp://chat?featuretype=start_chat&userType=VPA&vpa=$vpa&txnCategory=VPA2VPA&source=passbook_view_history
view_history_cta_deeplink_for_third_party_merchant_txn = paytmmp://chat?featuretype=start_chat&userType=VPA&vpa=$vpa&txnCategory=VPA2MERCHANT&source=passbook_view_history
view_history_cta_deeplink_for_ofus_merchant_txn = paytmmp://chat?featuretype=start_chat&userType=MERCHANT&mid=$merchantId&name=$name&source=passbook_view_history
view_history_cta_deeplink_for_onus_merchant_txn = paytmmp://mini-app?aId=7ee85e9b76c544f59ccc0c8b5fcdd655&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiL2luZGV4Lmh0bWwjL3BiL2hpc3RvcnktbGlzdCJ9&verticalId=$verticalId

# API URLs for Transaction History
pth.updates.api.url = https://upi.paytm.com/pth/ext/v1/updates
pth.listing.url = https://upi.paytm.com/pth/ext/v3/listing
pth.detail.url = https://upi.paytm.com/pth/ext/v3
pth.recon.api.url = https://upi.paytm.com/pth/ext/v1/recon
pth.get.tags.api.url = https://upi.paytm.com/pth/ext/v1/tag/getTags
pth.update.tags.api.url = https://upi.paytm.com/pth/ext/v1/tag/updateTags
pth.bg.listing.api.url = https://upi.paytm.com/pth/ext/v3/bg/listing
pth.mandate.history.api.url = https://upi.paytm.com/pth/ext/v1/mandateHistory
pth.toggle.visibility.api.url = https://upi.paytm.com/pth/ext/v3/toggleVisibility
pth.search.filter.api.url = https://upi.paytm.com/pth/ext/v3/listing/filter
pth.upi.passbook.api.url = https://upi.paytm.com/pth/ext/v3/upi/listing
pth.upi.lite.passbook.api.url = https://upi.paytm.com/pth/ext/v3/upi_lite/listing
pth.upi.cc.passbook.api.url = https://upi.paytm.com/pth/ext/v3/upi_cc/listing
pth.user.tags.summary.api.url = https://upi.paytm.com/pth/ext/v1/tag/getUserTagsSummary
pth.tag.summary.api.url = https://upi.paytm.com/pth/ext/v1/tag/tagSummary

toggleVisibility.feature.txnStatus.not.supported.list = PENDING
toggleVisibility.feature.txnType.not.supported.list =
toggleVisibility.feature.forOnusTxn.isSupported = true
toggleVisibility.feature.max.allowed.day.diff.supported = 45

autoTagging.details.aerospike.set.name = auto_tagging_details_set
# Expiry time in secs or -1 means no expiry
autoTagging.details.aerospike.expiry.time.in.secs = -1
autoTagging.aerospike.host-name = localhost
autoTagging.aerospike.port = 3000
autoTagging.aerospike.namespace = pth

tagging.user.tags.summary.from.date.in.days = 180
tagging.user.tags.summary.use.secondary.es = true
tagging.user.tags.summary.max.tags.size = 100
tagging.user.tags.summary.page.size = 100
tagging.user.tags.summary.exclude.hidden.txns = true

#this is month from this month new txn indicator field will be used It's in MM-YYYY format JiraId - PTH-1000
tagging.month.from.using.uth.visible.txn.indicator.for.aggregation = 05-2025

jwt.verification.supported.clients = UPI,PPBL,CST_HOME,pth_consumer_service,CST,TPAP_PANEL,UPI_SWITCH

txn.stream.lag.cache.enabled.open.sources = chat

# Listing CTA Configuration
listing.edit.tag.enabled = true
listing.repeat.payment.enabled = true
